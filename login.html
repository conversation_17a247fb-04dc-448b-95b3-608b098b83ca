<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>登录 - 战略性矿产资源数据运营服务平台</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="css/style.css">
</head>
<body class="bg-gradient-to-br from-blue-50 to-indigo-100 min-h-screen flex items-center justify-center">
    <div class="max-w-md w-full space-y-8 p-8">
        <!-- Logo和标题 -->
        <div class="text-center">
            <div class="mx-auto h-20 w-20 bg-blue-600 rounded-full flex items-center justify-center mb-4">
                <i class="fas fa-mountain text-white text-3xl"></i>
            </div>
            <h2 class="text-3xl font-bold text-gray-900 mb-2">系统登录</h2>
            <p class="text-gray-600">战略性矿产资源数据运营服务平台</p>
        </div>

        <!-- 登录表单 -->
        <div class="bg-white rounded-xl shadow-lg p-8">
            <form id="loginForm" class="space-y-6">
                <div>
                    <label for="username" class="block text-sm font-medium text-gray-700 mb-2">
                        <i class="fas fa-user mr-2"></i>用户名
                    </label>
                    <input 
                        id="username" 
                        name="username" 
                        type="text" 
                        required 
                        class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition duration-200"
                        placeholder="请输入用户名"
                        value="admin"
                    >
                </div>

                <div>
                    <label for="password" class="block text-sm font-medium text-gray-700 mb-2">
                        <i class="fas fa-lock mr-2"></i>密码
                    </label>
                    <div class="relative">
                        <input 
                            id="password" 
                            name="password" 
                            type="password" 
                            required 
                            class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition duration-200 pr-12"
                            placeholder="请输入密码"
                            value="123456"
                        >
                        <button 
                            type="button" 
                            id="togglePassword"
                            class="absolute inset-y-0 right-0 pr-3 flex items-center text-gray-400 hover:text-gray-600"
                        >
                            <i class="fas fa-eye"></i>
                        </button>
                    </div>
                </div>

                <div class="flex items-center justify-between">
                    <div class="flex items-center">
                        <input 
                            id="remember" 
                            name="remember" 
                            type="checkbox" 
                            class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                        >
                        <label for="remember" class="ml-2 block text-sm text-gray-700">
                            记住我
                        </label>
                    </div>
                    <div class="text-sm">
                        <a href="#" class="font-medium text-blue-600 hover:text-blue-500">
                            忘记密码？
                        </a>
                    </div>
                </div>

                <div>
                    <button 
                        type="submit" 
                        id="loginBtn"
                        class="w-full flex justify-center py-3 px-4 border border-transparent rounded-lg shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition duration-200"
                    >
                        <span id="loginText">登录</span>
                        <div id="loginLoading" class="loading ml-2 hidden"></div>
                    </button>
                </div>
            </form>

            <!-- 快速登录提示 -->
            <div class="mt-6 p-4 bg-blue-50 rounded-lg">
                <h4 class="text-sm font-medium text-blue-800 mb-2">
                    <i class="fas fa-info-circle mr-2"></i>演示账号
                </h4>
                <div class="text-sm text-blue-700 space-y-1">
                    <p><strong>管理员：</strong>admin / 123456</p>
                    <p><strong>普通用户：</strong>user / 123456</p>
                </div>
            </div>
        </div>

        <!-- 系统信息 -->
        <div class="text-center text-sm text-gray-500">
            <p>© 2024 战略性矿产资源数据运营服务平台</p>
            <p class="mt-1">版本 v1.0.0</p>
        </div>
    </div>

    <!-- 消息提示容器 -->
    <div id="toastContainer" class="fixed top-4 right-4 z-50"></div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            initLogin();
        });

        function initLogin() {
            // 密码显示/隐藏切换
            const togglePassword = document.getElementById('togglePassword');
            const passwordInput = document.getElementById('password');
            
            togglePassword.addEventListener('click', function() {
                const type = passwordInput.getAttribute('type') === 'password' ? 'text' : 'password';
                passwordInput.setAttribute('type', type);
                
                const icon = this.querySelector('i');
                icon.classList.toggle('fa-eye');
                icon.classList.toggle('fa-eye-slash');
            });

            // 登录表单提交
            const loginForm = document.getElementById('loginForm');
            loginForm.addEventListener('submit', function(e) {
                e.preventDefault();
                handleLogin();
            });

            // 回车键登录
            document.addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    handleLogin();
                }
            });
        }

        function handleLogin() {
            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;
            const loginBtn = document.getElementById('loginBtn');
            const loginText = document.getElementById('loginText');
            const loginLoading = document.getElementById('loginLoading');

            // 基本验证
            if (!username || !password) {
                showToast('请输入用户名和密码', 'warning');
                return;
            }

            // 显示加载状态
            loginBtn.disabled = true;
            loginText.textContent = '登录中...';
            loginLoading.classList.remove('hidden');

            // 模拟登录请求
            setTimeout(() => {
                // 简单的用户验证
                if ((username === 'admin' && password === '123456') || 
                    (username === 'user' && password === '123456')) {
                    
                    // 保存用户信息到localStorage
                    const userInfo = {
                        username: username,
                        role: username === 'admin' ? '管理员' : '普通用户',
                        loginTime: new Date().toISOString()
                    };
                    localStorage.setItem('userInfo', JSON.stringify(userInfo));
                    
                    showToast('登录成功，正在跳转...', 'success');
                    
                    // 跳转到主页面
                    setTimeout(() => {
                        window.location.href = 'index.html';
                    }, 1000);
                } else {
                    showToast('用户名或密码错误', 'error');
                    
                    // 恢复按钮状态
                    loginBtn.disabled = false;
                    loginText.textContent = '登录';
                    loginLoading.classList.add('hidden');
                }
            }, 1500);
        }

        function showToast(message, type = 'info', duration = 3000) {
            const toast = document.createElement('div');
            toast.className = `toast toast-${type} flex items-center`;
            
            const icon = getToastIcon(type);
            toast.innerHTML = `
                <i class="${icon} mr-2"></i>
                <span>${message}</span>
            `;
            
            const container = document.getElementById('toastContainer');
            container.appendChild(toast);
            
            // 自动移除提示
            setTimeout(() => {
                if (toast.parentNode) {
                    toast.style.animation = 'slideOut 0.3s ease-in';
                    setTimeout(() => {
                        if (toast.parentNode) {
                            toast.parentNode.removeChild(toast);
                        }
                    }, 300);
                }
            }, duration);
        }

        function getToastIcon(type) {
            const icons = {
                success: 'fas fa-check-circle',
                error: 'fas fa-exclamation-circle',
                warning: 'fas fa-exclamation-triangle',
                info: 'fas fa-info-circle'
            };
            return icons[type] || icons.info;
        }

        // 添加滑出动画
        const style = document.createElement('style');
        style.textContent = `
            @keyframes slideOut {
                from {
                    transform: translateX(0);
                    opacity: 1;
                }
                to {
                    transform: translateX(100%);
                    opacity: 0;
                }
            }
        `;
        document.head.appendChild(style);
    </script>
</body>
</html>
