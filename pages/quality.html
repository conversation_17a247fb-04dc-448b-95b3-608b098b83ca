<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>质量中心</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <link rel="stylesheet" href="../css/style.css">
</head>
<body class="bg-gray-100 p-6">
    <!-- 页面头部 -->
    <div class="mb-6">
        <div class="flex items-center justify-between">
            <div>
                <h1 class="text-2xl font-bold text-gray-900">数据质量中心</h1>
                <p class="text-gray-600 mt-1">全维度数据质量管理与监控</p>
            </div>
            <div class="flex items-center space-x-4">
                <button onclick="showCreateRuleModal()" class="btn btn-primary">
                    <i class="fas fa-plus mr-2"></i>创建质量规则
                </button>
                <button onclick="runQualityCheck()" class="btn btn-outline">
                    <i class="fas fa-play mr-2"></i>执行质量检查
                </button>
            </div>
        </div>
    </div>

    <!-- 质量概览 -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <div class="card">
            <div class="card-body">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-600">整体质量评分</p>
                        <p class="text-3xl font-bold text-green-600">89.5</p>
                        <p class="text-sm text-green-600 mt-1">↑ 2.3% 较上月</p>
                    </div>
                    <div class="p-3 bg-green-100 rounded-full">
                        <i class="fas fa-chart-line text-green-600 text-xl"></i>
                    </div>
                </div>
            </div>
        </div>
        <div class="card">
            <div class="card-body">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-600">质量规则数</p>
                        <p class="text-2xl font-bold text-gray-900">247</p>
                    </div>
                    <div class="p-3 bg-blue-100 rounded-full">
                        <i class="fas fa-rules text-blue-600 text-xl"></i>
                    </div>
                </div>
            </div>
        </div>
        <div class="card">
            <div class="card-body">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-600">异常数据量</p>
                        <p class="text-2xl font-bold text-red-600">1,234</p>
                        <p class="text-sm text-red-600 mt-1">↓ 15.2% 较上周</p>
                    </div>
                    <div class="p-3 bg-red-100 rounded-full">
                        <i class="fas fa-exclamation-triangle text-red-600 text-xl"></i>
                    </div>
                </div>
            </div>
        </div>
        <div class="card">
            <div class="card-body">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-600">检查任务数</p>
                        <p class="text-2xl font-bold text-purple-600">89</p>
                    </div>
                    <div class="p-3 bg-purple-100 rounded-full">
                        <i class="fas fa-tasks text-purple-600 text-xl"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 质量趋势图表 -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
        <div class="card">
            <div class="card-body">
                <h3 class="text-lg font-semibold mb-4">质量评分趋势</h3>
                <div class="chart-container">
                    <canvas id="qualityTrendChart"></canvas>
                </div>
            </div>
        </div>
        <div class="card">
            <div class="card-body">
                <h3 class="text-lg font-semibold mb-4">质量维度分布</h3>
                <div class="chart-container">
                    <canvas id="qualityDimensionChart"></canvas>
                </div>
            </div>
        </div>
    </div>

    <!-- 质量规则和检查结果 -->
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-8">
        <!-- 质量规则列表 -->
        <div class="lg:col-span-2">
            <div class="card">
                <div class="card-body">
                    <div class="flex items-center justify-between mb-4">
                        <h3 class="text-lg font-semibold">质量规则管理</h3>
                        <div class="flex items-center space-x-4">
                            <input type="text" id="ruleSearchInput" placeholder="搜索规则..." 
                                   class="form-input w-48">
                            <select id="ruleTypeFilter" class="form-select">
                                <option value="">全部类型</option>
                                <option value="completeness">完整性</option>
                                <option value="accuracy">准确性</option>
                                <option value="consistency">一致性</option>
                                <option value="timeliness">时效性</option>
                            </select>
                        </div>
                    </div>
                    
                    <div class="overflow-x-auto">
                        <table class="table">
                            <thead>
                                <tr>
                                    <th>规则名称</th>
                                    <th>类型</th>
                                    <th>数据表</th>
                                    <th>状态</th>
                                    <th>最后执行</th>
                                    <th>通过率</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody id="rulesTableBody">
                                <!-- 规则数据将通过JavaScript动态加载 -->
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <!-- 质量问题汇总 -->
        <div class="card">
            <div class="card-body">
                <h3 class="text-lg font-semibold mb-4">质量问题汇总</h3>
                <div class="space-y-4">
                    <div class="p-4 bg-red-50 border-l-4 border-red-500 rounded">
                        <div class="flex items-center justify-between">
                            <div>
                                <h4 class="font-medium text-red-800">严重问题</h4>
                                <p class="text-sm text-red-600">数据缺失率超过阈值</p>
                            </div>
                            <span class="text-2xl font-bold text-red-600">23</span>
                        </div>
                    </div>
                    <div class="p-4 bg-yellow-50 border-l-4 border-yellow-500 rounded">
                        <div class="flex items-center justify-between">
                            <div>
                                <h4 class="font-medium text-yellow-800">警告问题</h4>
                                <p class="text-sm text-yellow-600">数据格式不一致</p>
                            </div>
                            <span class="text-2xl font-bold text-yellow-600">156</span>
                        </div>
                    </div>
                    <div class="p-4 bg-blue-50 border-l-4 border-blue-500 rounded">
                        <div class="flex items-center justify-between">
                            <div>
                                <h4 class="font-medium text-blue-800">提示问题</h4>
                                <p class="text-sm text-blue-600">数据更新延迟</p>
                            </div>
                            <span class="text-2xl font-bold text-blue-600">89</span>
                        </div>
                    </div>
                </div>
                
                <div class="mt-6">
                    <h4 class="font-medium mb-3">问题趋势</h4>
                    <div class="space-y-2">
                        <div class="flex items-center justify-between text-sm">
                            <span>本周新增</span>
                            <span class="text-red-600">+45</span>
                        </div>
                        <div class="flex items-center justify-between text-sm">
                            <span>本周解决</span>
                            <span class="text-green-600">+67</span>
                        </div>
                        <div class="flex items-center justify-between text-sm">
                            <span>解决率</span>
                            <span class="text-blue-600">78.5%</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 最近检查记录 -->
    <div class="card">
        <div class="card-body">
            <h3 class="text-lg font-semibold mb-4">最近检查记录</h3>
            <div class="overflow-x-auto">
                <table class="table">
                    <thead>
                        <tr>
                            <th>检查任务</th>
                            <th>数据源</th>
                            <th>检查时间</th>
                            <th>检查规则数</th>
                            <th>通过规则数</th>
                            <th>异常数据量</th>
                            <th>状态</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody id="checkRecordsBody">
                        <!-- 检查记录将通过JavaScript动态加载 -->
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <script>
        let rulesData = [];
        let checkRecords = [];

        document.addEventListener('DOMContentLoaded', function() {
            initQuality();
        });

        function initQuality() {
            generateMockData();
            createCharts();
            renderRulesTable();
            renderCheckRecords();
            
            // 搜索和筛选功能
            document.getElementById('ruleSearchInput').addEventListener('input', renderRulesTable);
            document.getElementById('ruleTypeFilter').addEventListener('change', renderRulesTable);
        }

        function generateMockData() {
            // 生成质量规则数据
            const ruleTypes = ['completeness', 'accuracy', 'consistency', 'timeliness'];
            const typeLabels = { 
                completeness: '完整性', 
                accuracy: '准确性', 
                consistency: '一致性', 
                timeliness: '时效性' 
            };
            const tables = ['mineral_resources', 'production_data', 'quality_reports', 'safety_records'];
            const statuses = ['active', 'inactive', 'error'];
            const statusLabels = { active: '活跃', inactive: '非活跃', error: '错误' };

            for (let i = 1; i <= 247; i++) {
                const type = ruleTypes[Math.floor(Math.random() * ruleTypes.length)];
                const status = statuses[Math.floor(Math.random() * statuses.length)];
                const passRate = Math.floor(Math.random() * 40 + 60); // 60-100%
                
                rulesData.push({
                    id: i,
                    name: `质量规则_${i.toString().padStart(3, '0')}`,
                    type: type,
                    typeLabel: typeLabels[type],
                    table: tables[Math.floor(Math.random() * tables.length)],
                    status: status,
                    statusLabel: statusLabels[status],
                    lastRun: new Date(Date.now() - Math.random() * 86400000 * 7).toLocaleString('zh-CN'),
                    passRate: passRate
                });
            }

            // 生成检查记录数据
            for (let i = 1; i <= 20; i++) {
                const ruleCount = Math.floor(Math.random() * 20 + 5);
                const passCount = Math.floor(ruleCount * (Math.random() * 0.4 + 0.6));
                const status = passCount === ruleCount ? 'success' : passCount > ruleCount * 0.8 ? 'warning' : 'error';
                
                checkRecords.push({
                    id: i,
                    taskName: `质量检查任务_${i.toString().padStart(2, '0')}`,
                    dataSource: tables[Math.floor(Math.random() * tables.length)],
                    checkTime: new Date(Date.now() - Math.random() * 86400000 * 7).toLocaleString('zh-CN'),
                    ruleCount: ruleCount,
                    passCount: passCount,
                    errorCount: Math.floor(Math.random() * 1000 + 100),
                    status: status
                });
            }
        }

        function createCharts() {
            // 质量评分趋势图
            const trendCtx = document.getElementById('qualityTrendChart').getContext('2d');
            new Chart(trendCtx, {
                type: 'line',
                data: {
                    labels: ['1月', '2月', '3月', '4月', '5月', '6月'],
                    datasets: [{
                        label: '整体质量评分',
                        data: [85.2, 86.8, 87.5, 88.1, 87.9, 89.5],
                        borderColor: 'rgb(34, 197, 94)',
                        backgroundColor: 'rgba(34, 197, 94, 0.1)',
                        tension: 0.4,
                        fill: true
                    }, {
                        label: '完整性评分',
                        data: [88.5, 89.2, 90.1, 91.3, 90.8, 92.1],
                        borderColor: 'rgb(59, 130, 246)',
                        backgroundColor: 'rgba(59, 130, 246, 0.1)',
                        tension: 0.4
                    }, {
                        label: '准确性评分',
                        data: [82.1, 84.5, 85.2, 85.8, 85.1, 87.2],
                        borderColor: 'rgb(245, 158, 11)',
                        backgroundColor: 'rgba(245, 158, 11, 0.1)',
                        tension: 0.4
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: { legend: { position: 'bottom' } },
                    scales: {
                        y: { beginAtZero: false, min: 80, max: 100 }
                    }
                }
            });

            // 质量维度分布图
            const dimensionCtx = document.getElementById('qualityDimensionChart').getContext('2d');
            new Chart(dimensionCtx, {
                type: 'radar',
                data: {
                    labels: ['完整性', '准确性', '一致性', '时效性', '有效性', '唯一性'],
                    datasets: [{
                        label: '当前评分',
                        data: [92, 87, 89, 85, 91, 88],
                        borderColor: 'rgb(59, 130, 246)',
                        backgroundColor: 'rgba(59, 130, 246, 0.2)',
                        pointBackgroundColor: 'rgb(59, 130, 246)'
                    }, {
                        label: '目标评分',
                        data: [95, 90, 92, 88, 93, 90],
                        borderColor: 'rgb(34, 197, 94)',
                        backgroundColor: 'rgba(34, 197, 94, 0.1)',
                        pointBackgroundColor: 'rgb(34, 197, 94)'
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: { legend: { position: 'bottom' } },
                    scales: {
                        r: { beginAtZero: true, max: 100 }
                    }
                }
            });
        }

        function renderRulesTable() {
            const searchTerm = document.getElementById('ruleSearchInput').value.toLowerCase();
            const typeFilter = document.getElementById('ruleTypeFilter').value;
            
            let filteredData = rulesData.filter(item => {
                const matchesSearch = item.name.toLowerCase().includes(searchTerm) || 
                                    item.table.toLowerCase().includes(searchTerm);
                const matchesType = !typeFilter || item.type === typeFilter;
                return matchesSearch && matchesType;
            });
            
            const tbody = document.getElementById('rulesTableBody');
            tbody.innerHTML = filteredData.slice(0, 10).map(rule => `
                <tr class="hover:bg-gray-50">
                    <td class="font-medium">${rule.name}</td>
                    <td><span class="px-2 py-1 bg-blue-100 text-blue-800 rounded text-sm">${rule.typeLabel}</span></td>
                    <td>${rule.table}</td>
                    <td><span class="status-badge ${getStatusClass(rule.status)}">${rule.statusLabel}</span></td>
                    <td class="text-sm text-gray-500">${rule.lastRun}</td>
                    <td>
                        <div class="flex items-center">
                            <div class="w-16 bg-gray-200 rounded-full h-2 mr-2">
                                <div class="bg-green-600 h-2 rounded-full" style="width: ${rule.passRate}%"></div>
                            </div>
                            <span class="text-sm">${rule.passRate}%</span>
                        </div>
                    </td>
                    <td>
                        <div class="flex items-center space-x-2">
                            <button onclick="viewRule(${rule.id})" class="text-blue-600 hover:text-blue-800" title="查看详情">
                                <i class="fas fa-eye"></i>
                            </button>
                            <button onclick="editRule(${rule.id})" class="text-green-600 hover:text-green-800" title="编辑">
                                <i class="fas fa-edit"></i>
                            </button>
                            <button onclick="runRule(${rule.id})" class="text-purple-600 hover:text-purple-800" title="执行">
                                <i class="fas fa-play"></i>
                            </button>
                            <button onclick="deleteRule(${rule.id})" class="text-red-600 hover:text-red-800" title="删除">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                    </td>
                </tr>
            `).join('');
        }

        function renderCheckRecords() {
            const tbody = document.getElementById('checkRecordsBody');
            tbody.innerHTML = checkRecords.slice(0, 10).map(record => `
                <tr class="hover:bg-gray-50">
                    <td class="font-medium">${record.taskName}</td>
                    <td>${record.dataSource}</td>
                    <td class="text-sm text-gray-500">${record.checkTime}</td>
                    <td class="text-center">${record.ruleCount}</td>
                    <td class="text-center text-green-600">${record.passCount}</td>
                    <td class="text-center text-red-600">${record.errorCount.toLocaleString()}</td>
                    <td><span class="status-badge ${getStatusClass(record.status)}">${getStatusLabel(record.status)}</span></td>
                    <td>
                        <div class="flex items-center space-x-2">
                            <button onclick="viewCheckDetails(${record.id})" class="text-blue-600 hover:text-blue-800" title="查看详情">
                                <i class="fas fa-eye"></i>
                            </button>
                            <button onclick="downloadReport(${record.id})" class="text-green-600 hover:text-green-800" title="下载报告">
                                <i class="fas fa-download"></i>
                            </button>
                        </div>
                    </td>
                </tr>
            `).join('');
        }

        function getStatusClass(status) {
            const classes = {
                active: 'status-success',
                inactive: 'status-warning',
                error: 'status-error',
                success: 'status-success',
                warning: 'status-warning'
            };
            return classes[status] || 'status-info';
        }

        function getStatusLabel(status) {
            const labels = {
                active: '活跃',
                inactive: '非活跃',
                error: '错误',
                success: '成功',
                warning: '警告'
            };
            return labels[status] || status;
        }

        function showCreateRuleModal() {
            if (parent.showModal) {
                parent.showModal('创建质量规则', `
                    <form class="space-y-4">
                        <div class="form-group">
                            <label class="form-label">规则名称</label>
                            <input type="text" class="form-input" required>
                        </div>
                        <div class="grid grid-cols-2 gap-4">
                            <div class="form-group">
                                <label class="form-label">规则类型</label>
                                <select class="form-select" required>
                                    <option value="">请选择</option>
                                    <option value="completeness">完整性</option>
                                    <option value="accuracy">准确性</option>
                                    <option value="consistency">一致性</option>
                                    <option value="timeliness">时效性</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label class="form-label">数据表</label>
                                <select class="form-select" required>
                                    <option value="">请选择</option>
                                    <option value="mineral_resources">mineral_resources</option>
                                    <option value="production_data">production_data</option>
                                    <option value="quality_reports">quality_reports</option>
                                </select>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="form-label">规则描述</label>
                            <textarea class="form-input" rows="3"></textarea>
                        </div>
                        <div class="form-group">
                            <label class="form-label">检查条件</label>
                            <textarea class="form-input" rows="4" placeholder="请输入SQL条件或规则表达式"></textarea>
                        </div>
                        <div class="grid grid-cols-2 gap-4">
                            <div class="form-group">
                                <label class="form-label">告警阈值(%)</label>
                                <input type="number" class="form-input" min="0" max="100" value="80">
                            </div>
                            <div class="form-group">
                                <label class="form-label">执行频率</label>
                                <select class="form-select">
                                    <option value="daily">每日</option>
                                    <option value="weekly">每周</option>
                                    <option value="monthly">每月</option>
                                </select>
                            </div>
                        </div>
                    </form>
                `, [
                    { text: '取消', class: 'btn-outline' },
                    { text: '创建', class: 'btn-primary', onclick: 'createRule()' }
                ]);
            }
        }

        function createRule() {
            if (parent.showToast) {
                parent.showToast('质量规则创建成功', 'success');
            }
        }

        function runQualityCheck() {
            if (parent.showToast) {
                parent.showToast('质量检查任务已启动', 'info');
                setTimeout(() => {
                    parent.showToast('质量检查完成，发现23个问题', 'warning');
                }, 3000);
            }
        }

        function viewRule(id) {
            const rule = rulesData.find(item => item.id === id);
            if (parent.showModal && rule) {
                parent.showModal('规则详情', `
                    <div class="space-y-3">
                        <div><strong>规则名称：</strong>${rule.name}</div>
                        <div><strong>规则类型：</strong>${rule.typeLabel}</div>
                        <div><strong>数据表：</strong>${rule.table}</div>
                        <div><strong>状态：</strong><span class="status-badge ${getStatusClass(rule.status)}">${rule.statusLabel}</span></div>
                        <div><strong>通过率：</strong>${rule.passRate}%</div>
                        <div><strong>最后执行：</strong>${rule.lastRun}</div>
                        <div><strong>规则描述：</strong>检查${rule.table}表的${rule.typeLabel}问题</div>
                    </div>
                `);
            }
        }

        function editRule(id) {
            if (parent.showToast) {
                parent.showToast('编辑功能开发中', 'info');
            }
        }

        function runRule(id) {
            if (parent.showConfirm) {
                parent.showConfirm('确定要执行这个质量规则吗？', function() {
                    if (parent.showToast) {
                        parent.showToast('规则执行成功', 'success');
                    }
                });
            }
        }

        function deleteRule(id) {
            if (parent.showConfirm) {
                parent.showConfirm('确定要删除这个质量规则吗？', function() {
                    if (parent.showToast) {
                        parent.showToast('规则删除成功', 'success');
                    }
                });
            }
        }

        function viewCheckDetails(id) {
            const record = checkRecords.find(item => item.id === id);
            if (parent.showModal && record) {
                parent.showModal('检查详情', `
                    <div class="space-y-3">
                        <div><strong>任务名称：</strong>${record.taskName}</div>
                        <div><strong>数据源：</strong>${record.dataSource}</div>
                        <div><strong>检查时间：</strong>${record.checkTime}</div>
                        <div><strong>检查规则数：</strong>${record.ruleCount}</div>
                        <div><strong>通过规则数：</strong>${record.passCount}</div>
                        <div><strong>异常数据量：</strong>${record.errorCount.toLocaleString()}</div>
                        <div><strong>通过率：</strong>${Math.round((record.passCount / record.ruleCount) * 100)}%</div>
                    </div>
                `);
            }
        }

        function downloadReport(id) {
            if (parent.showToast) {
                parent.showToast('质量报告下载功能开发中', 'info');
            }
        }
    </script>
</body>
</html>
