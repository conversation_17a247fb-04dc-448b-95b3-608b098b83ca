<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>权限管理</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="../css/style.css">
</head>
<body class="bg-gray-100 p-6">
    <!-- 页面头部 -->
    <div class="mb-6">
        <div class="flex items-center justify-between">
            <div>
                <h1 class="text-2xl font-bold text-gray-900">统一权限管理</h1>
                <p class="text-gray-600 mt-1">全域统一权限管控体系</p>
            </div>
            <div class="flex items-center space-x-4">
                <button onclick="showCreatePolicyModal()" class="btn btn-primary">
                    <i class="fas fa-plus mr-2"></i>创建权限策略
                </button>
                <button onclick="syncPermissions()" class="btn btn-outline">
                    <i class="fas fa-sync-alt mr-2"></i>同步权限
                </button>
            </div>
        </div>
    </div>

    <!-- 权限概览 -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <div class="card">
            <div class="card-body">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-600">权限策略数</p>
                        <p class="text-2xl font-bold text-gray-900">156</p>
                    </div>
                    <div class="p-3 bg-blue-100 rounded-full">
                        <i class="fas fa-shield-alt text-blue-600 text-xl"></i>
                    </div>
                </div>
            </div>
        </div>
        <div class="card">
            <div class="card-body">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-600">活跃用户数</p>
                        <p class="text-2xl font-bold text-green-600">298</p>
                    </div>
                    <div class="p-3 bg-green-100 rounded-full">
                        <i class="fas fa-users text-green-600 text-xl"></i>
                    </div>
                </div>
            </div>
        </div>
        <div class="card">
            <div class="card-body">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-600">资源类型数</p>
                        <p class="text-2xl font-bold text-purple-600">12</p>
                    </div>
                    <div class="p-3 bg-purple-100 rounded-full">
                        <i class="fas fa-database text-purple-600 text-xl"></i>
                    </div>
                </div>
            </div>
        </div>
        <div class="card">
            <div class="card-body">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-600">今日访问次数</p>
                        <p class="text-2xl font-bold text-orange-600">2.3K</p>
                    </div>
                    <div class="p-3 bg-orange-100 rounded-full">
                        <i class="fas fa-eye text-orange-600 text-xl"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 权限管理主要内容 -->
    <div class="grid grid-cols-1 lg:grid-cols-4 gap-6">
        <!-- 左侧资源树 -->
        <div class="card">
            <div class="card-body">
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-lg font-semibold">资源目录</h3>
                    <button onclick="expandAllResources()" class="text-sm text-blue-600 hover:text-blue-800">
                        <i class="fas fa-expand-arrows-alt mr-1"></i>展开全部
                    </button>
                </div>
                
                <div class="space-y-2" id="resourceTree">
                    <!-- 资源树将通过JavaScript动态生成 -->
                </div>
            </div>
        </div>

        <!-- 权限策略列表 -->
        <div class="lg:col-span-3">
            <div class="card">
                <div class="card-body">
                    <div class="flex items-center justify-between mb-4">
                        <h3 class="text-lg font-semibold">权限策略管理</h3>
                        <div class="flex items-center space-x-4">
                            <div class="relative">
                                <input type="text" id="searchInput" placeholder="搜索策略..." 
                                       class="form-input pl-10 pr-4 py-2 w-64">
                                <i class="fas fa-search absolute left-3 top-3 text-gray-400"></i>
                            </div>
                            <select id="resourceFilter" class="form-select">
                                <option value="">全部资源</option>
                                <option value="hive">Hive</option>
                                <option value="spark">Spark</option>
                                <option value="flink">Flink</option>
                                <option value="mysql">MySQL</option>
                                <option value="hdfs">HDFS</option>
                            </select>
                            <select id="statusFilter" class="form-select">
                                <option value="">全部状态</option>
                                <option value="active">生效中</option>
                                <option value="inactive">已停用</option>
                                <option value="expired">已过期</option>
                            </select>
                        </div>
                    </div>
                    
                    <div class="overflow-x-auto">
                        <table class="table" id="permissionTable">
                            <thead>
                                <tr>
                                    <th>策略名称</th>
                                    <th>资源类型</th>
                                    <th>权限主体</th>
                                    <th>权限操作</th>
                                    <th>状态</th>
                                    <th>生效时间</th>
                                    <th>过期时间</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody id="permissionTableBody">
                                <!-- 权限数据将通过JavaScript动态加载 -->
                            </tbody>
                        </table>
                    </div>
                    
                    <!-- 分页 -->
                    <div class="flex items-center justify-between mt-4">
                        <div class="text-sm text-gray-700">
                            显示 <span id="pageInfo">1-10</span> 条，共 <span id="totalCount">156</span> 条记录
                        </div>
                        <div class="flex items-center space-x-2">
                            <button onclick="changePage(-1)" class="btn btn-outline btn-sm">
                                <i class="fas fa-chevron-left"></i>
                            </button>
                            <span id="currentPage" class="px-3 py-1 bg-blue-100 text-blue-800 rounded">1</span>
                            <button onclick="changePage(1)" class="btn btn-outline btn-sm">
                                <i class="fas fa-chevron-right"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 权限审计日志 -->
    <div class="card mt-6">
        <div class="card-body">
            <h3 class="text-lg font-semibold mb-4">权限审计日志</h3>
            <div class="overflow-x-auto">
                <table class="table">
                    <thead>
                        <tr>
                            <th>操作时间</th>
                            <th>操作用户</th>
                            <th>操作类型</th>
                            <th>资源路径</th>
                            <th>操作结果</th>
                            <th>客户端IP</th>
                            <th>详情</th>
                        </tr>
                    </thead>
                    <tbody id="auditLogBody">
                        <!-- 审计日志将通过JavaScript动态加载 -->
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <script>
        let currentPage = 1;
        const pageSize = 10;
        let permissionData = [];
        let auditLogData = [];
        let resourceData = {};

        document.addEventListener('DOMContentLoaded', function() {
            initPermissions();
        });

        function initPermissions() {
            generateMockData();
            renderResourceTree();
            renderPermissionTable();
            renderAuditLog();
            
            // 搜索和筛选功能
            document.getElementById('searchInput').addEventListener('input', renderPermissionTable);
            document.getElementById('resourceFilter').addEventListener('change', renderPermissionTable);
            document.getElementById('statusFilter').addEventListener('change', renderPermissionTable);
        }

        function generateMockData() {
            // 生成资源数据
            resourceData = {
                'hive': {
                    name: 'Hive数据仓库',
                    icon: 'fas fa-database',
                    children: {
                        'default': { name: 'default数据库', children: ['mineral_resources', 'production_data', 'quality_reports'] },
                        'analytics': { name: 'analytics数据库', children: ['user_behavior', 'sales_data', 'market_trends'] }
                    }
                },
                'spark': {
                    name: 'Spark计算引擎',
                    icon: 'fas fa-bolt',
                    children: {
                        'jobs': { name: '作业管理', children: ['etl_jobs', 'ml_jobs', 'batch_jobs'] },
                        'queues': { name: '队列管理', children: ['default_queue', 'high_priority', 'low_priority'] }
                    }
                },
                'hdfs': {
                    name: 'HDFS存储',
                    icon: 'fas fa-folder',
                    children: {
                        'data': { name: '数据目录', children: ['/data/raw', '/data/processed', '/data/archive'] },
                        'logs': { name: '日志目录', children: ['/logs/app', '/logs/system', '/logs/audit'] }
                    }
                }
            };

            // 生成权限策略数据
            const resources = ['hive', 'spark', 'flink', 'mysql', 'hdfs'];
            const subjects = ['张三', '李四', '王五', '数据分析组', '开发团队', '管理员组'];
            const operations = ['SELECT', 'INSERT', 'UPDATE', 'DELETE', 'CREATE', 'DROP', 'EXECUTE'];
            const statuses = ['active', 'inactive', 'expired'];
            const statusLabels = { active: '生效中', inactive: '已停用', expired: '已过期' };

            for (let i = 1; i <= 156; i++) {
                const resource = resources[Math.floor(Math.random() * resources.length)];
                const status = statuses[Math.floor(Math.random() * statuses.length)];
                const effectiveDate = new Date(Date.now() - Math.random() * 86400000 * 30);
                const expireDate = new Date(effectiveDate.getTime() + Math.random() * 86400000 * 365);
                
                permissionData.push({
                    id: i,
                    name: `权限策略_${i.toString().padStart(3, '0')}`,
                    resource: resource,
                    subject: subjects[Math.floor(Math.random() * subjects.length)],
                    operations: operations.slice(0, Math.floor(Math.random() * 4 + 1)),
                    status: status,
                    statusLabel: statusLabels[status],
                    effectiveTime: effectiveDate.toLocaleString('zh-CN'),
                    expireTime: expireDate.toLocaleString('zh-CN')
                });
            }

            // 生成审计日志数据
            const logOperations = ['SELECT', 'INSERT', 'CREATE_TABLE', 'DROP_TABLE', 'GRANT', 'REVOKE'];
            const logResults = ['SUCCESS', 'FAILED', 'DENIED'];
            const users = ['张三', '李四', '王五', '赵六', 'admin'];

            for (let i = 1; i <= 50; i++) {
                const result = logResults[Math.floor(Math.random() * logResults.length)];
                auditLogData.push({
                    id: i,
                    time: new Date(Date.now() - Math.random() * 86400000 * 7).toLocaleString('zh-CN'),
                    user: users[Math.floor(Math.random() * users.length)],
                    operation: logOperations[Math.floor(Math.random() * logOperations.length)],
                    resource: `/data/warehouse/table_${Math.floor(Math.random() * 100)}`,
                    result: result,
                    ip: `192.168.1.${Math.floor(Math.random() * 254 + 1)}`
                });
            }
        }

        function renderResourceTree() {
            const container = document.getElementById('resourceTree');
            const html = Object.entries(resourceData).map(([key, resource]) => `
                <div class="resource-item">
                    <div class="flex items-center p-2 hover:bg-gray-50 rounded cursor-pointer" 
                         onclick="toggleResource('${key}')">
                        <i class="fas fa-chevron-right transform transition-transform mr-2" id="chevron-${key}"></i>
                        <i class="${resource.icon} text-blue-600 mr-2"></i>
                        <span class="font-medium">${resource.name}</span>
                    </div>
                    <div class="ml-6 hidden" id="children-${key}">
                        ${Object.entries(resource.children).map(([childKey, child]) => `
                            <div class="p-2 hover:bg-gray-50 rounded cursor-pointer">
                                <div class="flex items-center">
                                    <i class="fas fa-folder text-yellow-600 mr-2"></i>
                                    <span>${child.name}</span>
                                </div>
                                <div class="ml-6 mt-1">
                                    ${child.children.map(item => `
                                        <div class="flex items-center p-1 text-sm text-gray-600 hover:text-blue-600">
                                            <i class="fas fa-file text-gray-400 mr-2"></i>
                                            <span>${item}</span>
                                        </div>
                                    `).join('')}
                                </div>
                            </div>
                        `).join('')}
                    </div>
                </div>
            `).join('');
            
            container.innerHTML = html;
        }

        function toggleResource(resourceKey) {
            const chevron = document.getElementById(`chevron-${resourceKey}`);
            const children = document.getElementById(`children-${resourceKey}`);
            
            if (children.classList.contains('hidden')) {
                children.classList.remove('hidden');
                chevron.style.transform = 'rotate(90deg)';
            } else {
                children.classList.add('hidden');
                chevron.style.transform = 'rotate(0deg)';
            }
        }

        function expandAllResources() {
            Object.keys(resourceData).forEach(key => {
                const children = document.getElementById(`children-${key}`);
                const chevron = document.getElementById(`chevron-${key}`);
                if (children && chevron) {
                    children.classList.remove('hidden');
                    chevron.style.transform = 'rotate(90deg)';
                }
            });
        }

        function renderPermissionTable() {
            const searchTerm = document.getElementById('searchInput').value.toLowerCase();
            const resourceFilter = document.getElementById('resourceFilter').value;
            const statusFilter = document.getElementById('statusFilter').value;
            
            let filteredData = permissionData.filter(item => {
                const matchesSearch = item.name.toLowerCase().includes(searchTerm) || 
                                    item.subject.toLowerCase().includes(searchTerm);
                const matchesResource = !resourceFilter || item.resource === resourceFilter;
                const matchesStatus = !statusFilter || item.status === statusFilter;
                return matchesSearch && matchesResource && matchesStatus;
            });
            
            const startIndex = (currentPage - 1) * pageSize;
            const endIndex = startIndex + pageSize;
            const pageData = filteredData.slice(startIndex, endIndex);
            
            const tbody = document.getElementById('permissionTableBody');
            tbody.innerHTML = pageData.map(permission => `
                <tr class="hover:bg-gray-50">
                    <td class="font-medium">${permission.name}</td>
                    <td>
                        <span class="px-2 py-1 bg-blue-100 text-blue-800 rounded text-sm uppercase">${permission.resource}</span>
                    </td>
                    <td>${permission.subject}</td>
                    <td>
                        <div class="flex flex-wrap gap-1">
                            ${permission.operations.map(op => `
                                <span class="px-2 py-1 bg-green-100 text-green-800 rounded text-xs">${op}</span>
                            `).join('')}
                        </div>
                    </td>
                    <td>
                        <span class="status-badge ${getStatusClass(permission.status)}">${permission.statusLabel}</span>
                    </td>
                    <td class="text-sm text-gray-500">${permission.effectiveTime}</td>
                    <td class="text-sm text-gray-500">${permission.expireTime}</td>
                    <td>
                        <div class="flex items-center space-x-2">
                            <button onclick="viewPermission(${permission.id})" class="text-blue-600 hover:text-blue-800" title="查看详情">
                                <i class="fas fa-eye"></i>
                            </button>
                            <button onclick="editPermission(${permission.id})" class="text-green-600 hover:text-green-800" title="编辑">
                                <i class="fas fa-edit"></i>
                            </button>
                            ${permission.status === 'active' ? 
                                `<button onclick="revokePermission(${permission.id})" class="text-red-600 hover:text-red-800" title="撤销">
                                    <i class="fas fa-ban"></i>
                                </button>` :
                                `<button onclick="activatePermission(${permission.id})" class="text-green-600 hover:text-green-800" title="激活">
                                    <i class="fas fa-check"></i>
                                </button>`
                            }
                            <button onclick="deletePermission(${permission.id})" class="text-red-600 hover:text-red-800" title="删除">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                    </td>
                </tr>
            `).join('');
            
            // 更新分页信息
            document.getElementById('pageInfo').textContent = `${startIndex + 1}-${Math.min(endIndex, filteredData.length)}`;
            document.getElementById('totalCount').textContent = filteredData.length;
            document.getElementById('currentPage').textContent = currentPage;
        }

        function renderAuditLog() {
            const tbody = document.getElementById('auditLogBody');
            tbody.innerHTML = auditLogData.slice(0, 10).map(log => `
                <tr class="hover:bg-gray-50">
                    <td class="text-sm text-gray-500">${log.time}</td>
                    <td class="font-medium">${log.user}</td>
                    <td><span class="px-2 py-1 bg-blue-100 text-blue-800 rounded text-sm">${log.operation}</span></td>
                    <td class="text-sm font-mono">${log.resource}</td>
                    <td>
                        <span class="status-badge ${getResultClass(log.result)}">${log.result}</span>
                    </td>
                    <td class="text-sm text-gray-500">${log.ip}</td>
                    <td>
                        <button onclick="viewLogDetails(${log.id})" class="text-blue-600 hover:text-blue-800">
                            <i class="fas fa-info-circle"></i>
                        </button>
                    </td>
                </tr>
            `).join('');
        }

        function getStatusClass(status) {
            const classes = {
                active: 'status-success',
                inactive: 'status-warning',
                expired: 'status-error'
            };
            return classes[status] || 'status-info';
        }

        function getResultClass(result) {
            const classes = {
                SUCCESS: 'status-success',
                FAILED: 'status-error',
                DENIED: 'status-warning'
            };
            return classes[result] || 'status-info';
        }

        function changePage(direction) {
            const newPage = currentPage + direction;
            if (newPage >= 1 && newPage <= Math.ceil(permissionData.length / pageSize)) {
                currentPage = newPage;
                renderPermissionTable();
            }
        }

        function showCreatePolicyModal() {
            if (parent.showModal) {
                parent.showModal('创建权限策略', `
                    <form class="space-y-4">
                        <div class="form-group">
                            <label class="form-label">策略名称</label>
                            <input type="text" class="form-input" required>
                        </div>
                        <div class="grid grid-cols-2 gap-4">
                            <div class="form-group">
                                <label class="form-label">资源类型</label>
                                <select class="form-select" required>
                                    <option value="">请选择</option>
                                    <option value="hive">Hive</option>
                                    <option value="spark">Spark</option>
                                    <option value="flink">Flink</option>
                                    <option value="mysql">MySQL</option>
                                    <option value="hdfs">HDFS</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label class="form-label">权限主体</label>
                                <input type="text" class="form-input" placeholder="用户名或组名" required>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="form-label">资源路径</label>
                            <input type="text" class="form-input" placeholder="如: /data/warehouse/table_name" required>
                        </div>
                        <div class="form-group">
                            <label class="form-label">权限操作</label>
                            <div class="grid grid-cols-4 gap-2 mt-2">
                                <label class="flex items-center">
                                    <input type="checkbox" class="mr-2" value="SELECT">
                                    <span class="text-sm">SELECT</span>
                                </label>
                                <label class="flex items-center">
                                    <input type="checkbox" class="mr-2" value="INSERT">
                                    <span class="text-sm">INSERT</span>
                                </label>
                                <label class="flex items-center">
                                    <input type="checkbox" class="mr-2" value="UPDATE">
                                    <span class="text-sm">UPDATE</span>
                                </label>
                                <label class="flex items-center">
                                    <input type="checkbox" class="mr-2" value="DELETE">
                                    <span class="text-sm">DELETE</span>
                                </label>
                                <label class="flex items-center">
                                    <input type="checkbox" class="mr-2" value="CREATE">
                                    <span class="text-sm">CREATE</span>
                                </label>
                                <label class="flex items-center">
                                    <input type="checkbox" class="mr-2" value="DROP">
                                    <span class="text-sm">DROP</span>
                                </label>
                                <label class="flex items-center">
                                    <input type="checkbox" class="mr-2" value="EXECUTE">
                                    <span class="text-sm">EXECUTE</span>
                                </label>
                                <label class="flex items-center">
                                    <input type="checkbox" class="mr-2" value="ALL">
                                    <span class="text-sm">ALL</span>
                                </label>
                            </div>
                        </div>
                        <div class="grid grid-cols-2 gap-4">
                            <div class="form-group">
                                <label class="form-label">生效时间</label>
                                <input type="datetime-local" class="form-input" required>
                            </div>
                            <div class="form-group">
                                <label class="form-label">过期时间</label>
                                <input type="datetime-local" class="form-input">
                            </div>
                        </div>
                    </form>
                `, [
                    { text: '取消', class: 'btn-outline' },
                    { text: '创建', class: 'btn-primary', onclick: 'createPolicy()' }
                ]);
            }
        }

        function createPolicy() {
            if (parent.showToast) {
                parent.showToast('权限策略创建成功', 'success');
            }
        }

        function syncPermissions() {
            if (parent.showToast) {
                parent.showToast('权限同步已启动', 'info');
                setTimeout(() => {
                    parent.showToast('权限同步完成', 'success');
                }, 2000);
            }
        }

        function viewPermission(id) {
            const permission = permissionData.find(item => item.id === id);
            if (parent.showModal && permission) {
                parent.showModal('权限详情', `
                    <div class="space-y-3">
                        <div><strong>策略名称：</strong>${permission.name}</div>
                        <div><strong>资源类型：</strong>${permission.resource.toUpperCase()}</div>
                        <div><strong>权限主体：</strong>${permission.subject}</div>
                        <div><strong>权限操作：</strong>${permission.operations.join(', ')}</div>
                        <div><strong>状态：</strong><span class="status-badge ${getStatusClass(permission.status)}">${permission.statusLabel}</span></div>
                        <div><strong>生效时间：</strong>${permission.effectiveTime}</div>
                        <div><strong>过期时间：</strong>${permission.expireTime}</div>
                    </div>
                `);
            }
        }

        function editPermission(id) {
            if (parent.showToast) {
                parent.showToast('编辑功能开发中', 'info');
            }
        }

        function revokePermission(id) {
            if (parent.showConfirm) {
                parent.showConfirm('确定要撤销这个权限策略吗？', function() {
                    if (parent.showToast) {
                        parent.showToast('权限策略已撤销', 'warning');
                    }
                });
            }
        }

        function activatePermission(id) {
            if (parent.showConfirm) {
                parent.showConfirm('确定要激活这个权限策略吗？', function() {
                    if (parent.showToast) {
                        parent.showToast('权限策略已激活', 'success');
                    }
                });
            }
        }

        function deletePermission(id) {
            if (parent.showConfirm) {
                parent.showConfirm('确定要删除这个权限策略吗？删除后无法恢复。', function() {
                    if (parent.showToast) {
                        parent.showToast('权限策略删除成功', 'success');
                    }
                });
            }
        }

        function viewLogDetails(id) {
            const log = auditLogData.find(item => item.id === id);
            if (parent.showModal && log) {
                parent.showModal('审计日志详情', `
                    <div class="space-y-3">
                        <div><strong>操作时间：</strong>${log.time}</div>
                        <div><strong>操作用户：</strong>${log.user}</div>
                        <div><strong>操作类型：</strong>${log.operation}</div>
                        <div><strong>资源路径：</strong>${log.resource}</div>
                        <div><strong>操作结果：</strong><span class="status-badge ${getResultClass(log.result)}">${log.result}</span></div>
                        <div><strong>客户端IP：</strong>${log.ip}</div>
                        <div><strong>用户代理：</strong>Mozilla/5.0 (Windows NT 10.0; Win64; x64)</div>
                        <div><strong>会话ID：</strong>sess_${Math.random().toString(36).substr(2, 9)}</div>
                    </div>
                `);
            }
        }
    </script>
</body>
</html>
