<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>用户管理</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="../css/style.css">
</head>
<body class="bg-gray-100 p-6">
    <!-- 页面头部 -->
    <div class="mb-6">
        <div class="flex items-center justify-between">
            <div>
                <h1 class="text-2xl font-bold text-gray-900">用户管理</h1>
                <p class="text-gray-600 mt-1">管理系统用户和权限分配</p>
            </div>
            <div class="flex items-center space-x-4">
                <button onclick="showAddUserModal()" class="btn btn-primary">
                    <i class="fas fa-user-plus mr-2"></i>添加用户
                </button>
                <button onclick="exportUsers()" class="btn btn-outline">
                    <i class="fas fa-download mr-2"></i>导出用户
                </button>
            </div>
        </div>
    </div>

    <!-- 用户统计 -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <div class="card">
            <div class="card-body">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-600">总用户数</p>
                        <p class="text-2xl font-bold text-gray-900">342</p>
                    </div>
                    <div class="p-3 bg-blue-100 rounded-full">
                        <i class="fas fa-users text-blue-600 text-xl"></i>
                    </div>
                </div>
            </div>
        </div>
        <div class="card">
            <div class="card-body">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-600">活跃用户</p>
                        <p class="text-2xl font-bold text-green-600">298</p>
                    </div>
                    <div class="p-3 bg-green-100 rounded-full">
                        <i class="fas fa-user-check text-green-600 text-xl"></i>
                    </div>
                </div>
            </div>
        </div>
        <div class="card">
            <div class="card-body">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-600">在线用户</p>
                        <p class="text-2xl font-bold text-purple-600">156</p>
                    </div>
                    <div class="p-3 bg-purple-100 rounded-full">
                        <i class="fas fa-circle text-purple-600 text-xl"></i>
                    </div>
                </div>
            </div>
        </div>
        <div class="card">
            <div class="card-body">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-600">本月新增</p>
                        <p class="text-2xl font-bold text-orange-600">23</p>
                    </div>
                    <div class="p-3 bg-orange-100 rounded-full">
                        <i class="fas fa-user-plus text-orange-600 text-xl"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 用户列表 -->
    <div class="card">
        <div class="card-body">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg font-semibold">用户列表</h3>
                <div class="flex items-center space-x-4">
                    <div class="relative">
                        <input type="text" id="searchInput" placeholder="搜索用户..." 
                               class="form-input pl-10 pr-4 py-2 w-64">
                        <i class="fas fa-search absolute left-3 top-3 text-gray-400"></i>
                    </div>
                    <select id="roleFilter" class="form-select">
                        <option value="">全部角色</option>
                        <option value="admin">管理员</option>
                        <option value="manager">经理</option>
                        <option value="analyst">分析师</option>
                        <option value="operator">操作员</option>
                        <option value="viewer">查看者</option>
                    </select>
                    <select id="statusFilter" class="form-select">
                        <option value="">全部状态</option>
                        <option value="active">活跃</option>
                        <option value="inactive">非活跃</option>
                        <option value="locked">已锁定</option>
                    </select>
                    <select id="departmentFilter" class="form-select">
                        <option value="">全部部门</option>
                        <option value="production">生产部</option>
                        <option value="quality">质量部</option>
                        <option value="finance">财务部</option>
                        <option value="safety">安全部</option>
                        <option value="it">信息部</option>
                    </select>
                </div>
            </div>
            
            <div class="overflow-x-auto">
                <table class="table" id="userTable">
                    <thead>
                        <tr>
                            <th>
                                <input type="checkbox" id="selectAll" class="rounded">
                            </th>
                            <th>用户信息</th>
                            <th>角色</th>
                            <th>部门</th>
                            <th>状态</th>
                            <th>最后登录</th>
                            <th>创建时间</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody id="userTableBody">
                        <!-- 数据将通过JavaScript动态加载 -->
                    </tbody>
                </table>
            </div>
            
            <!-- 批量操作 -->
            <div class="flex items-center justify-between mt-4">
                <div class="flex items-center space-x-4">
                    <span class="text-sm text-gray-600">已选择 <span id="selectedCount">0</span> 个用户</span>
                    <div class="flex items-center space-x-2">
                        <button onclick="batchActivate()" class="btn btn-sm btn-success">
                            <i class="fas fa-check mr-1"></i>批量激活
                        </button>
                        <button onclick="batchDeactivate()" class="btn btn-sm btn-warning">
                            <i class="fas fa-pause mr-1"></i>批量停用
                        </button>
                        <button onclick="batchDelete()" class="btn btn-sm btn-danger">
                            <i class="fas fa-trash mr-1"></i>批量删除
                        </button>
                    </div>
                </div>
                
                <!-- 分页 -->
                <div class="flex items-center space-x-4">
                    <div class="text-sm text-gray-700">
                        显示 <span id="pageInfo">1-10</span> 条，共 <span id="totalCount">342</span> 条记录
                    </div>
                    <div class="flex items-center space-x-2">
                        <button onclick="changePage(-1)" class="btn btn-outline btn-sm">
                            <i class="fas fa-chevron-left"></i>
                        </button>
                        <span id="currentPage" class="px-3 py-1 bg-blue-100 text-blue-800 rounded">1</span>
                        <button onclick="changePage(1)" class="btn btn-outline btn-sm">
                            <i class="fas fa-chevron-right"></i>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        let currentPage = 1;
        const pageSize = 10;
        let userData = [];
        let selectedUsers = new Set();

        document.addEventListener('DOMContentLoaded', function() {
            initUsers();
        });

        function initUsers() {
            generateMockData();
            renderUserTable();
            
            // 搜索和筛选功能
            document.getElementById('searchInput').addEventListener('input', renderUserTable);
            document.getElementById('roleFilter').addEventListener('change', renderUserTable);
            document.getElementById('statusFilter').addEventListener('change', renderUserTable);
            document.getElementById('departmentFilter').addEventListener('change', renderUserTable);
            
            // 全选功能
            document.getElementById('selectAll').addEventListener('change', function() {
                const checkboxes = document.querySelectorAll('tbody input[type="checkbox"]');
                checkboxes.forEach(cb => {
                    cb.checked = this.checked;
                    if (this.checked) {
                        selectedUsers.add(parseInt(cb.value));
                    } else {
                        selectedUsers.delete(parseInt(cb.value));
                    }
                });
                updateSelectedCount();
            });
        }

        function generateMockData() {
            const roles = ['admin', 'manager', 'analyst', 'operator', 'viewer'];
            const roleLabels = { 
                admin: '管理员', 
                manager: '经理', 
                analyst: '分析师', 
                operator: '操作员', 
                viewer: '查看者' 
            };
            const departments = ['production', 'quality', 'finance', 'safety', 'it'];
            const departmentLabels = { 
                production: '生产部', 
                quality: '质量部', 
                finance: '财务部', 
                safety: '安全部', 
                it: '信息部' 
            };
            const statuses = ['active', 'inactive', 'locked'];
            const statusLabels = { 
                active: '活跃', 
                inactive: '非活跃', 
                locked: '已锁定' 
            };
            const firstNames = ['张', '李', '王', '赵', '钱', '孙', '周', '吴', '郑', '冯'];
            const lastNames = ['伟', '芳', '娜', '敏', '静', '丽', '强', '磊', '军', '洋'];

            for (let i = 1; i <= 342; i++) {
                const role = roles[Math.floor(Math.random() * roles.length)];
                const department = departments[Math.floor(Math.random() * departments.length)];
                const status = statuses[Math.floor(Math.random() * statuses.length)];
                const firstName = firstNames[Math.floor(Math.random() * firstNames.length)];
                const lastName = lastNames[Math.floor(Math.random() * lastNames.length)];
                const name = firstName + lastName;
                
                userData.push({
                    id: i,
                    username: `user${i.toString().padStart(3, '0')}`,
                    name: name,
                    email: `${name.toLowerCase()}@company.com`,
                    phone: `138${Math.floor(Math.random() * *********).toString().padStart(8, '0')}`,
                    role: role,
                    roleLabel: roleLabels[role],
                    department: department,
                    departmentLabel: departmentLabels[department],
                    status: status,
                    statusLabel: statusLabels[status],
                    lastLogin: status === 'active' ? 
                        new Date(Date.now() - Math.random() * 86400000 * 7).toLocaleString('zh-CN') : 
                        status === 'inactive' ? 
                        new Date(Date.now() - Math.random() * 86400000 * 30).toLocaleString('zh-CN') : 
                        '从未登录',
                    createTime: new Date(Date.now() - Math.random() * 86400000 * 365).toLocaleString('zh-CN'),
                    avatar: `https://ui-avatars.com/api/?name=${encodeURIComponent(name)}&background=random&color=fff`
                });
            }
        }

        function renderUserTable() {
            const searchTerm = document.getElementById('searchInput').value.toLowerCase();
            const roleFilter = document.getElementById('roleFilter').value;
            const statusFilter = document.getElementById('statusFilter').value;
            const departmentFilter = document.getElementById('departmentFilter').value;
            
            let filteredData = userData.filter(item => {
                const matchesSearch = item.name.toLowerCase().includes(searchTerm) || 
                                    item.username.toLowerCase().includes(searchTerm) ||
                                    item.email.toLowerCase().includes(searchTerm);
                const matchesRole = !roleFilter || item.role === roleFilter;
                const matchesStatus = !statusFilter || item.status === statusFilter;
                const matchesDepartment = !departmentFilter || item.department === departmentFilter;
                return matchesSearch && matchesRole && matchesStatus && matchesDepartment;
            });
            
            const startIndex = (currentPage - 1) * pageSize;
            const endIndex = startIndex + pageSize;
            const pageData = filteredData.slice(startIndex, endIndex);
            
            const tbody = document.getElementById('userTableBody');
            tbody.innerHTML = pageData.map(user => `
                <tr class="hover:bg-gray-50">
                    <td>
                        <input type="checkbox" value="${user.id}" class="rounded" onchange="toggleUserSelection(${user.id})">
                    </td>
                    <td>
                        <div class="flex items-center space-x-3">
                            <img src="${user.avatar}" alt="${user.name}" class="w-10 h-10 rounded-full">
                            <div>
                                <div class="font-medium text-gray-900">${user.name}</div>
                                <div class="text-sm text-gray-500">${user.username}</div>
                                <div class="text-sm text-gray-500">${user.email}</div>
                            </div>
                        </div>
                    </td>
                    <td>
                        <span class="px-2 py-1 bg-blue-100 text-blue-800 rounded text-sm">${user.roleLabel}</span>
                    </td>
                    <td>
                        <span class="px-2 py-1 bg-gray-100 text-gray-800 rounded text-sm">${user.departmentLabel}</span>
                    </td>
                    <td>
                        <span class="status-badge ${getStatusClass(user.status)}">${user.statusLabel}</span>
                    </td>
                    <td class="text-sm text-gray-500">${user.lastLogin}</td>
                    <td class="text-sm text-gray-500">${user.createTime}</td>
                    <td>
                        <div class="flex items-center space-x-2">
                            <button onclick="viewUser(${user.id})" class="text-blue-600 hover:text-blue-800" title="查看详情">
                                <i class="fas fa-eye"></i>
                            </button>
                            <button onclick="editUser(${user.id})" class="text-green-600 hover:text-green-800" title="编辑">
                                <i class="fas fa-edit"></i>
                            </button>
                            <button onclick="resetPassword(${user.id})" class="text-orange-600 hover:text-orange-800" title="重置密码">
                                <i class="fas fa-key"></i>
                            </button>
                            ${user.status === 'active' ? 
                                `<button onclick="lockUser(${user.id})" class="text-red-600 hover:text-red-800" title="锁定用户">
                                    <i class="fas fa-lock"></i>
                                </button>` :
                                `<button onclick="unlockUser(${user.id})" class="text-green-600 hover:text-green-800" title="解锁用户">
                                    <i class="fas fa-unlock"></i>
                                </button>`
                            }
                            <button onclick="deleteUser(${user.id})" class="text-red-600 hover:text-red-800" title="删除用户">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                    </td>
                </tr>
            `).join('');
            
            // 更新分页信息
            document.getElementById('pageInfo').textContent = `${startIndex + 1}-${Math.min(endIndex, filteredData.length)}`;
            document.getElementById('totalCount').textContent = filteredData.length;
            document.getElementById('currentPage').textContent = currentPage;
        }

        function getStatusClass(status) {
            const classes = {
                active: 'status-success',
                inactive: 'status-warning',
                locked: 'status-error'
            };
            return classes[status] || 'status-info';
        }

        function toggleUserSelection(userId) {
            if (selectedUsers.has(userId)) {
                selectedUsers.delete(userId);
            } else {
                selectedUsers.add(userId);
            }
            updateSelectedCount();
        }

        function updateSelectedCount() {
            document.getElementById('selectedCount').textContent = selectedUsers.size;
        }

        function changePage(direction) {
            const newPage = currentPage + direction;
            if (newPage >= 1 && newPage <= Math.ceil(userData.length / pageSize)) {
                currentPage = newPage;
                renderUserTable();
            }
        }

        function showAddUserModal() {
            if (parent.showModal) {
                parent.showModal('添加用户', `
                    <form class="space-y-4">
                        <div class="grid grid-cols-2 gap-4">
                            <div class="form-group">
                                <label class="form-label">用户名</label>
                                <input type="text" class="form-input" required>
                            </div>
                            <div class="form-group">
                                <label class="form-label">姓名</label>
                                <input type="text" class="form-input" required>
                            </div>
                        </div>
                        <div class="grid grid-cols-2 gap-4">
                            <div class="form-group">
                                <label class="form-label">邮箱</label>
                                <input type="email" class="form-input" required>
                            </div>
                            <div class="form-group">
                                <label class="form-label">手机号</label>
                                <input type="tel" class="form-input" required>
                            </div>
                        </div>
                        <div class="grid grid-cols-2 gap-4">
                            <div class="form-group">
                                <label class="form-label">角色</label>
                                <select class="form-select" required>
                                    <option value="">请选择</option>
                                    <option value="admin">管理员</option>
                                    <option value="manager">经理</option>
                                    <option value="analyst">分析师</option>
                                    <option value="operator">操作员</option>
                                    <option value="viewer">查看者</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label class="form-label">部门</label>
                                <select class="form-select" required>
                                    <option value="">请选择</option>
                                    <option value="production">生产部</option>
                                    <option value="quality">质量部</option>
                                    <option value="finance">财务部</option>
                                    <option value="safety">安全部</option>
                                    <option value="it">信息部</option>
                                </select>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="form-label">初始密码</label>
                            <input type="password" class="form-input" value="123456" required>
                        </div>
                    </form>
                `, [
                    { text: '取消', class: 'btn-outline' },
                    { text: '添加', class: 'btn-primary', onclick: 'addUser()' }
                ]);
            }
        }

        function addUser() {
            if (parent.showToast) {
                parent.showToast('用户添加成功', 'success');
            }
        }

        function viewUser(id) {
            const user = userData.find(item => item.id === id);
            if (parent.showModal && user) {
                parent.showModal('用户详情', `
                    <div class="space-y-4">
                        <div class="flex items-center space-x-4">
                            <img src="${user.avatar}" alt="${user.name}" class="w-16 h-16 rounded-full">
                            <div>
                                <h4 class="text-lg font-semibold">${user.name}</h4>
                                <p class="text-gray-600">${user.username}</p>
                            </div>
                        </div>
                        <div class="grid grid-cols-2 gap-4 text-sm">
                            <div><strong>邮箱：</strong>${user.email}</div>
                            <div><strong>手机：</strong>${user.phone}</div>
                            <div><strong>角色：</strong>${user.roleLabel}</div>
                            <div><strong>部门：</strong>${user.departmentLabel}</div>
                            <div><strong>状态：</strong><span class="status-badge ${getStatusClass(user.status)}">${user.statusLabel}</span></div>
                            <div><strong>最后登录：</strong>${user.lastLogin}</div>
                            <div><strong>创建时间：</strong>${user.createTime}</div>
                        </div>
                    </div>
                `);
            }
        }

        function editUser(id) {
            if (parent.showToast) {
                parent.showToast('编辑功能开发中', 'info');
            }
        }

        function resetPassword(id) {
            if (parent.showConfirm) {
                parent.showConfirm('确定要重置该用户的密码吗？新密码将发送到用户邮箱。', function() {
                    if (parent.showToast) {
                        parent.showToast('密码重置成功，已发送到用户邮箱', 'success');
                    }
                });
            }
        }

        function lockUser(id) {
            if (parent.showConfirm) {
                parent.showConfirm('确定要锁定该用户吗？', function() {
                    if (parent.showToast) {
                        parent.showToast('用户已锁定', 'warning');
                    }
                });
            }
        }

        function unlockUser(id) {
            if (parent.showConfirm) {
                parent.showConfirm('确定要解锁该用户吗？', function() {
                    if (parent.showToast) {
                        parent.showToast('用户已解锁', 'success');
                    }
                });
            }
        }

        function deleteUser(id) {
            if (parent.showConfirm) {
                parent.showConfirm('确定要删除该用户吗？删除后无法恢复。', function() {
                    if (parent.showToast) {
                        parent.showToast('用户删除成功', 'success');
                    }
                });
            }
        }

        function batchActivate() {
            if (selectedUsers.size === 0) {
                if (parent.showToast) {
                    parent.showToast('请先选择要操作的用户', 'warning');
                }
                return;
            }
            if (parent.showConfirm) {
                parent.showConfirm(`确定要激活选中的 ${selectedUsers.size} 个用户吗？`, function() {
                    if (parent.showToast) {
                        parent.showToast(`已激活 ${selectedUsers.size} 个用户`, 'success');
                    }
                    selectedUsers.clear();
                    updateSelectedCount();
                });
            }
        }

        function batchDeactivate() {
            if (selectedUsers.size === 0) {
                if (parent.showToast) {
                    parent.showToast('请先选择要操作的用户', 'warning');
                }
                return;
            }
            if (parent.showConfirm) {
                parent.showConfirm(`确定要停用选中的 ${selectedUsers.size} 个用户吗？`, function() {
                    if (parent.showToast) {
                        parent.showToast(`已停用 ${selectedUsers.size} 个用户`, 'warning');
                    }
                    selectedUsers.clear();
                    updateSelectedCount();
                });
            }
        }

        function batchDelete() {
            if (selectedUsers.size === 0) {
                if (parent.showToast) {
                    parent.showToast('请先选择要操作的用户', 'warning');
                }
                return;
            }
            if (parent.showConfirm) {
                parent.showConfirm(`确定要删除选中的 ${selectedUsers.size} 个用户吗？删除后无法恢复。`, function() {
                    if (parent.showToast) {
                        parent.showToast(`已删除 ${selectedUsers.size} 个用户`, 'success');
                    }
                    selectedUsers.clear();
                    updateSelectedCount();
                });
            }
        }

        function exportUsers() {
            if (parent.showToast) {
                parent.showToast('用户数据导出功能开发中', 'info');
            }
        }
    </script>
</body>
</html>
