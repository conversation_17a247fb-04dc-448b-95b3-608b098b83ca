<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>数据开发</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/monaco-editor/0.34.1/min/vs/loader.min.js"></script>
    <link rel="stylesheet" href="../css/style.css">
    <style>
        .editor-container {
            height: 400px;
            border: 1px solid #e5e7eb;
            border-radius: 0.5rem;
        }
        .result-container {
            height: 200px;
            overflow-y: auto;
            background-color: #1f2937;
            color: #f9fafb;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            padding: 1rem;
            border-radius: 0.5rem;
        }
        .file-tree {
            max-height: 400px;
            overflow-y: auto;
        }
        .file-item {
            cursor: pointer;
            padding: 0.25rem 0.5rem;
            border-radius: 0.25rem;
            transition: background-color 0.2s;
        }
        .file-item:hover {
            background-color: #f3f4f6;
        }
        .file-item.active {
            background-color: #dbeafe;
            color: #1d4ed8;
        }
        .tab-content {
            display: none;
        }
        .tab-content.active {
            display: block;
        }
    </style>
</head>
<body class="bg-gray-100 p-6">
    <!-- 页面头部 -->
    <div class="mb-6">
        <div class="flex items-center justify-between">
            <div>
                <h1 class="text-2xl font-bold text-gray-900">数据开发工作台</h1>
                <p class="text-gray-600 mt-1">可视化数据开发与SQL编辑器</p>
            </div>
            <div class="flex items-center space-x-4">
                <button onclick="createNewScript()" class="btn btn-primary">
                    <i class="fas fa-plus mr-2"></i>新建脚本
                </button>
                <button onclick="saveScript()" class="btn btn-outline">
                    <i class="fas fa-save mr-2"></i>保存
                </button>
                <button onclick="runScript()" class="btn btn-success">
                    <i class="fas fa-play mr-2"></i>执行
                </button>
            </div>
        </div>
    </div>

    <!-- 主要工作区域 -->
    <div class="grid grid-cols-12 gap-6 h-screen">
        <!-- 左侧文件树和数据源 -->
        <div class="col-span-3 space-y-6">
            <!-- 项目文件树 -->
            <div class="card">
                <div class="card-body">
                    <div class="flex items-center justify-between mb-4">
                        <h3 class="text-lg font-semibold">项目文件</h3>
                        <button onclick="refreshFileTree()" class="text-gray-500 hover:text-gray-700">
                            <i class="fas fa-sync-alt"></i>
                        </button>
                    </div>
                    <div class="file-tree" id="fileTree">
                        <!-- 文件树将动态加载 -->
                    </div>
                </div>
            </div>

            <!-- 数据源连接 -->
            <div class="card">
                <div class="card-body">
                    <h3 class="text-lg font-semibold mb-4">数据源</h3>
                    <div class="space-y-2" id="dataSources">
                        <!-- 数据源列表将动态加载 -->
                    </div>
                </div>
            </div>
        </div>

        <!-- 中间编辑器区域 -->
        <div class="col-span-6 space-y-6">
            <!-- 编辑器标签页 -->
            <div class="card">
                <div class="card-body p-0">
                    <div class="flex items-center border-b">
                        <div class="flex-1 flex items-center" id="editorTabs">
                            <div class="tab-item active px-4 py-3 border-r cursor-pointer" data-tab="sql">
                                <i class="fas fa-database mr-2"></i>SQL编辑器
                            </div>
                            <div class="tab-item px-4 py-3 border-r cursor-pointer" data-tab="python">
                                <i class="fab fa-python mr-2"></i>Python脚本
                            </div>
                            <div class="tab-item px-4 py-3 cursor-pointer" data-tab="scala">
                                <i class="fas fa-code mr-2"></i>Scala脚本
                            </div>
                        </div>
                        <div class="px-4 py-3">
                            <button onclick="formatCode()" class="text-gray-500 hover:text-gray-700 mr-3" title="格式化代码">
                                <i class="fas fa-indent"></i>
                            </button>
                            <button onclick="toggleFullscreen()" class="text-gray-500 hover:text-gray-700" title="全屏">
                                <i class="fas fa-expand"></i>
                            </button>
                        </div>
                    </div>
                    
                    <!-- SQL编辑器 -->
                    <div id="sqlEditor" class="tab-content active">
                        <div class="editor-container" id="sqlEditorContainer"></div>
                    </div>
                    
                    <!-- Python编辑器 -->
                    <div id="pythonEditor" class="tab-content">
                        <div class="editor-container" id="pythonEditorContainer"></div>
                    </div>
                    
                    <!-- Scala编辑器 -->
                    <div id="scalaEditor" class="tab-content">
                        <div class="editor-container" id="scalaEditorContainer"></div>
                    </div>
                </div>
            </div>

            <!-- 执行结果 -->
            <div class="card">
                <div class="card-body">
                    <div class="flex items-center justify-between mb-4">
                        <h3 class="text-lg font-semibold">执行结果</h3>
                        <div class="flex items-center space-x-2">
                            <span class="text-sm text-gray-500">执行时间: <span id="executionTime">--</span></span>
                            <button onclick="clearResults()" class="text-gray-500 hover:text-gray-700">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                    </div>
                    <div class="result-container" id="resultContainer">
                        <div class="text-gray-400">等待执行...</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 右侧工具面板 -->
        <div class="col-span-3 space-y-6">
            <!-- 表结构浏览器 -->
            <div class="card">
                <div class="card-body">
                    <h3 class="text-lg font-semibold mb-4">表结构</h3>
                    <div class="space-y-2" id="tableStructure">
                        <div class="text-gray-500 text-sm">选择数据源查看表结构</div>
                    </div>
                </div>
            </div>

            <!-- 执行历史 -->
            <div class="card">
                <div class="card-body">
                    <h3 class="text-lg font-semibold mb-4">执行历史</h3>
                    <div class="space-y-2" id="executionHistory">
                        <!-- 执行历史将动态加载 -->
                    </div>
                </div>
            </div>

            <!-- 代码片段 -->
            <div class="card">
                <div class="card-body">
                    <h3 class="text-lg font-semibold mb-4">代码片段</h3>
                    <div class="space-y-2" id="codeSnippets">
                        <!-- 代码片段将动态加载 -->
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        let editors = {};
        let currentTab = 'sql';
        let executionHistory = [];

        document.addEventListener('DOMContentLoaded', function() {
            initDevelopment();
        });

        function initDevelopment() {
            initMonacoEditor();
            loadFileTree();
            loadDataSources();
            loadExecutionHistory();
            loadCodeSnippets();
            setupTabSwitching();
        }

        function initMonacoEditor() {
            require.config({ paths: { vs: 'https://cdnjs.cloudflare.com/ajax/libs/monaco-editor/0.34.1/min/vs' } });
            require(['vs/editor/editor.main'], function () {
                // SQL编辑器
                editors.sql = monaco.editor.create(document.getElementById('sqlEditorContainer'), {
                    value: `-- 矿产资源数据查询示例
SELECT 
    mineral_type,
    region,
    SUM(reserves) as total_reserves,
    AVG(grade) as avg_grade
FROM mineral_resources 
WHERE discovery_date >= '2020-01-01'
GROUP BY mineral_type, region
ORDER BY total_reserves DESC
LIMIT 10;`,
                    language: 'sql',
                    theme: 'vs',
                    automaticLayout: true,
                    minimap: { enabled: false },
                    scrollBeyondLastLine: false
                });

                // Python编辑器
                editors.python = monaco.editor.create(document.getElementById('pythonEditorContainer'), {
                    value: `# 矿产数据分析脚本
import pandas as pd
import numpy as np
from pyspark.sql import SparkSession

# 初始化Spark会话
spark = SparkSession.builder.appName("MineralDataAnalysis").getOrCreate()

# 读取数据
df = spark.read.table("mineral_resources")

# 数据分析
result = df.groupBy("mineral_type", "region") \\
           .agg({"reserves": "sum", "grade": "avg"}) \\
           .orderBy("sum(reserves)", ascending=False)

# 显示结果
result.show(10)`,
                    language: 'python',
                    theme: 'vs',
                    automaticLayout: true,
                    minimap: { enabled: false },
                    scrollBeyondLastLine: false
                });

                // Scala编辑器
                editors.scala = monaco.editor.create(document.getElementById('scalaEditorContainer'), {
                    value: `// Spark Scala数据处理脚本
import org.apache.spark.sql.SparkSession
import org.apache.spark.sql.functions._

// 创建Spark会话
val spark = SparkSession.builder()
  .appName("MineralDataProcessing")
  .getOrCreate()

import spark.implicits._

// 读取数据
val mineralDF = spark.read.table("mineral_resources")

// 数据处理
val result = mineralDF
  .filter($"discovery_date" >= "2020-01-01")
  .groupBy($"mineral_type", $"region")
  .agg(
    sum($"reserves").alias("total_reserves"),
    avg($"grade").alias("avg_grade")
  )
  .orderBy($"total_reserves".desc)
  .limit(10)

// 显示结果
result.show()`,
                    language: 'scala',
                    theme: 'vs',
                    automaticLayout: true,
                    minimap: { enabled: false },
                    scrollBeyondLastLine: false
                });
            });
        }

        function loadFileTree() {
            const fileTree = document.getElementById('fileTree');
            const files = [
                { name: 'sql', type: 'folder', children: [
                    { name: 'mineral_analysis.sql', type: 'file', icon: 'fas fa-database' },
                    { name: 'production_report.sql', type: 'file', icon: 'fas fa-database' },
                    { name: 'quality_check.sql', type: 'file', icon: 'fas fa-database' }
                ]},
                { name: 'python', type: 'folder', children: [
                    { name: 'data_processing.py', type: 'file', icon: 'fab fa-python' },
                    { name: 'ml_analysis.py', type: 'file', icon: 'fab fa-python' },
                    { name: 'etl_pipeline.py', type: 'file', icon: 'fab fa-python' }
                ]},
                { name: 'scala', type: 'folder', children: [
                    { name: 'spark_job.scala', type: 'file', icon: 'fas fa-code' },
                    { name: 'streaming_app.scala', type: 'file', icon: 'fas fa-code' }
                ]},
                { name: 'config', type: 'folder', children: [
                    { name: 'database.conf', type: 'file', icon: 'fas fa-cog' },
                    { name: 'spark.conf', type: 'file', icon: 'fas fa-cog' }
                ]}
            ];

            function renderFileTree(items, level = 0) {
                return items.map(item => {
                    if (item.type === 'folder') {
                        return `
                            <div class="ml-${level * 4}">
                                <div class="file-item flex items-center" onclick="toggleFolder(this)">
                                    <i class="fas fa-chevron-right mr-2 transform transition-transform"></i>
                                    <i class="fas fa-folder text-yellow-600 mr-2"></i>
                                    <span>${item.name}</span>
                                </div>
                                <div class="hidden">
                                    ${renderFileTree(item.children, level + 1)}
                                </div>
                            </div>
                        `;
                    } else {
                        return `
                            <div class="ml-${(level + 1) * 4}">
                                <div class="file-item flex items-center" onclick="openFile('${item.name}')">
                                    <i class="${item.icon} text-blue-600 mr-2"></i>
                                    <span>${item.name}</span>
                                </div>
                            </div>
                        `;
                    }
                }).join('');
            }

            fileTree.innerHTML = renderFileTree(files);
        }

        function loadDataSources() {
            const dataSources = document.getElementById('dataSources');
            const sources = [
                { name: 'MySQL主库', type: 'mysql', status: 'connected' },
                { name: 'Hive数据仓库', type: 'hive', status: 'connected' },
                { name: 'Oracle生产库', type: 'oracle', status: 'connected' },
                { name: 'MongoDB集群', type: 'mongodb', status: 'disconnected' }
            ];

            dataSources.innerHTML = sources.map(source => `
                <div class="flex items-center justify-between p-2 border rounded hover:bg-gray-50 cursor-pointer"
                     onclick="selectDataSource('${source.name}')">
                    <div class="flex items-center">
                        <i class="fas fa-database text-blue-600 mr-2"></i>
                        <span class="text-sm">${source.name}</span>
                    </div>
                    <span class="w-2 h-2 rounded-full ${source.status === 'connected' ? 'bg-green-500' : 'bg-red-500'}"></span>
                </div>
            `).join('');
        }

        function loadExecutionHistory() {
            const history = [
                { query: 'SELECT * FROM mineral_resources...', time: '2分钟前', status: 'success' },
                { query: 'UPDATE production_data SET...', time: '5分钟前', status: 'success' },
                { query: 'CREATE TABLE quality_reports...', time: '10分钟前', status: 'error' },
                { query: 'SELECT COUNT(*) FROM...', time: '15分钟前', status: 'success' }
            ];

            const historyContainer = document.getElementById('executionHistory');
            historyContainer.innerHTML = history.map(item => `
                <div class="p-2 border rounded text-sm hover:bg-gray-50 cursor-pointer"
                     onclick="loadHistoryQuery('${item.query}')">
                    <div class="flex items-center justify-between mb-1">
                        <span class="text-xs text-gray-500">${item.time}</span>
                        <i class="fas fa-${item.status === 'success' ? 'check text-green-500' : 'times text-red-500'}"></i>
                    </div>
                    <div class="truncate">${item.query}</div>
                </div>
            `).join('');
        }

        function loadCodeSnippets() {
            const snippets = [
                { name: '基础查询', code: 'SELECT * FROM table_name WHERE condition;' },
                { name: '聚合统计', code: 'SELECT column, COUNT(*) FROM table GROUP BY column;' },
                { name: '连接查询', code: 'SELECT a.*, b.* FROM table_a a JOIN table_b b ON a.id = b.id;' },
                { name: '窗口函数', code: 'SELECT *, ROW_NUMBER() OVER (PARTITION BY column ORDER BY date) FROM table;' }
            ];

            const snippetsContainer = document.getElementById('codeSnippets');
            snippetsContainer.innerHTML = snippets.map(snippet => `
                <div class="p-2 border rounded text-sm hover:bg-gray-50 cursor-pointer"
                     onclick="insertSnippet('${snippet.code}')">
                    <div class="font-medium">${snippet.name}</div>
                    <div class="text-xs text-gray-500 truncate">${snippet.code}</div>
                </div>
            `).join('');
        }

        function setupTabSwitching() {
            document.querySelectorAll('.tab-item').forEach(tab => {
                tab.addEventListener('click', function() {
                    const tabName = this.dataset.tab;
                    switchTab(tabName);
                });
            });
        }

        function switchTab(tabName) {
            // 更新标签页状态
            document.querySelectorAll('.tab-item').forEach(tab => {
                tab.classList.remove('active');
            });
            document.querySelector(`[data-tab="${tabName}"]`).classList.add('active');

            // 更新内容区域
            document.querySelectorAll('.tab-content').forEach(content => {
                content.classList.remove('active');
            });
            document.getElementById(`${tabName}Editor`).classList.add('active');

            currentTab = tabName;
        }

        function toggleFolder(element) {
            const chevron = element.querySelector('.fa-chevron-right');
            const folder = element.nextElementSibling;
            
            if (folder.classList.contains('hidden')) {
                folder.classList.remove('hidden');
                chevron.style.transform = 'rotate(90deg)';
            } else {
                folder.classList.add('hidden');
                chevron.style.transform = 'rotate(0deg)';
            }
        }

        function openFile(filename) {
            if (parent.showToast) {
                parent.showToast(`打开文件: ${filename}`, 'info');
            }
        }

        function selectDataSource(sourceName) {
            // 模拟加载表结构
            const tableStructure = document.getElementById('tableStructure');
            const tables = [
                { name: 'mineral_resources', columns: ['id', 'mineral_type', 'region', 'reserves', 'grade'] },
                { name: 'production_data', columns: ['id', 'date', 'output', 'quality_score'] },
                { name: 'quality_reports', columns: ['id', 'report_date', 'status', 'issues'] }
            ];

            tableStructure.innerHTML = `
                <div class="text-sm font-medium mb-2">${sourceName}</div>
                ${tables.map(table => `
                    <div class="mb-3">
                        <div class="flex items-center cursor-pointer" onclick="toggleTable(this)">
                            <i class="fas fa-chevron-right mr-2 transform transition-transform"></i>
                            <i class="fas fa-table text-blue-600 mr-2"></i>
                            <span class="font-medium">${table.name}</span>
                        </div>
                        <div class="hidden ml-6 mt-1">
                            ${table.columns.map(col => `
                                <div class="text-xs text-gray-600 py-1 cursor-pointer hover:text-blue-600"
                                     onclick="insertColumn('${table.name}.${col}')">${col}</div>
                            `).join('')}
                        </div>
                    </div>
                `).join('')}
            `;
        }

        function toggleTable(element) {
            const chevron = element.querySelector('.fa-chevron-right');
            const columns = element.nextElementSibling;
            
            if (columns.classList.contains('hidden')) {
                columns.classList.remove('hidden');
                chevron.style.transform = 'rotate(90deg)';
            } else {
                columns.classList.add('hidden');
                chevron.style.transform = 'rotate(0deg)';
            }
        }

        function insertColumn(columnName) {
            if (editors[currentTab]) {
                const editor = editors[currentTab];
                const position = editor.getPosition();
                editor.executeEdits('', [{
                    range: new monaco.Range(position.lineNumber, position.column, position.lineNumber, position.column),
                    text: columnName
                }]);
                editor.focus();
            }
        }

        function insertSnippet(code) {
            if (editors[currentTab]) {
                const editor = editors[currentTab];
                const position = editor.getPosition();
                editor.executeEdits('', [{
                    range: new monaco.Range(position.lineNumber, position.column, position.lineNumber, position.column),
                    text: code
                }]);
                editor.focus();
            }
        }

        function loadHistoryQuery(query) {
            if (editors[currentTab]) {
                editors[currentTab].setValue(query);
            }
        }

        function createNewScript() {
            if (parent.showModal) {
                parent.showModal('新建脚本', `
                    <form class="space-y-4">
                        <div class="form-group">
                            <label class="form-label">脚本名称</label>
                            <input type="text" class="form-input" required>
                        </div>
                        <div class="form-group">
                            <label class="form-label">脚本类型</label>
                            <select class="form-select" required>
                                <option value="sql">SQL脚本</option>
                                <option value="python">Python脚本</option>
                                <option value="scala">Scala脚本</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label class="form-label">描述</label>
                            <textarea class="form-input" rows="3"></textarea>
                        </div>
                    </form>
                `, [
                    { text: '取消', class: 'btn-outline' },
                    { text: '创建', class: 'btn-primary', onclick: 'doCreateScript()' }
                ]);
            }
        }

        function doCreateScript() {
            if (parent.showToast) {
                parent.showToast('脚本创建成功', 'success');
            }
        }

        function saveScript() {
            if (parent.showToast) {
                parent.showToast('脚本保存成功', 'success');
            }
        }

        function runScript() {
            const resultContainer = document.getElementById('resultContainer');
            const executionTimeElement = document.getElementById('executionTime');
            
            // 显示执行中状态
            resultContainer.innerHTML = '<div class="text-yellow-400">执行中...</div>';
            
            // 模拟执行
            setTimeout(() => {
                const mockResults = [
                    '执行开始...',
                    '连接数据源: MySQL主库',
                    '查询执行中...',
                    '',
                    '查询结果:',
                    '+----------------+----------+---------------+----------+',
                    '| mineral_type   | region   | total_reserves| avg_grade|',
                    '+----------------+----------+---------------+----------+',
                    '| 铁矿石         | 华北     | 1250000       | 65.2     |',
                    '| 煤炭           | 山西     | 2800000       | 78.5     |',
                    '| 铜矿           | 西南     | 450000        | 45.8     |',
                    '+----------------+----------+---------------+----------+',
                    '',
                    '查询完成，返回 3 行记录',
                    '执行时间: 1.23秒'
                ];
                
                resultContainer.innerHTML = mockResults.map(line => `<div>${line}</div>`).join('');
                executionTimeElement.textContent = '1.23s';
                
                if (parent.showToast) {
                    parent.showToast('脚本执行完成', 'success');
                }
            }, 2000);
        }

        function formatCode() {
            if (editors[currentTab]) {
                editors[currentTab].getAction('editor.action.formatDocument').run();
            }
        }

        function toggleFullscreen() {
            const editorContainer = document.querySelector('.col-span-6');
            if (editorContainer.classList.contains('col-span-6')) {
                editorContainer.classList.remove('col-span-6');
                editorContainer.classList.add('col-span-12');
                document.querySelector('.col-span-3:first-child').style.display = 'none';
                document.querySelector('.col-span-3:last-child').style.display = 'none';
            } else {
                editorContainer.classList.remove('col-span-12');
                editorContainer.classList.add('col-span-6');
                document.querySelector('.col-span-3:first-child').style.display = 'block';
                document.querySelector('.col-span-3:last-child').style.display = 'block';
            }
            
            // 重新调整编辑器大小
            setTimeout(() => {
                Object.values(editors).forEach(editor => {
                    if (editor) editor.layout();
                });
            }, 100);
        }

        function clearResults() {
            document.getElementById('resultContainer').innerHTML = '<div class="text-gray-400">等待执行...</div>';
            document.getElementById('executionTime').textContent = '--';
        }

        function refreshFileTree() {
            if (parent.showToast) {
                parent.showToast('文件树刷新完成', 'success');
            }
        }
    </script>
</body>
</html>
