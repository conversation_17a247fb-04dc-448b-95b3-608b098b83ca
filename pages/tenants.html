<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>租户管理</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <link rel="stylesheet" href="../css/style.css">
</head>
<body class="bg-gray-100 p-6">
    <!-- 页面头部 -->
    <div class="mb-6">
        <div class="flex items-center justify-between">
            <div>
                <h1 class="text-2xl font-bold text-gray-900">租户管理</h1>
                <p class="text-gray-600 mt-1">多租户隔离管理与资源分配</p>
            </div>
            <div class="flex items-center space-x-4">
                <button onclick="showCreateTenantModal()" class="btn btn-primary">
                    <i class="fas fa-plus mr-2"></i>创建租户
                </button>
                <button onclick="exportTenants()" class="btn btn-outline">
                    <i class="fas fa-download mr-2"></i>导出数据
                </button>
            </div>
        </div>
    </div>

    <!-- 租户概览统计 -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <div class="card">
            <div class="card-body">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-600">总租户数</p>
                        <p class="text-2xl font-bold text-gray-900">28</p>
                    </div>
                    <div class="p-3 bg-blue-100 rounded-full">
                        <i class="fas fa-building text-blue-600 text-xl"></i>
                    </div>
                </div>
            </div>
        </div>
        <div class="card">
            <div class="card-body">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-600">活跃租户</p>
                        <p class="text-2xl font-bold text-green-600">24</p>
                    </div>
                    <div class="p-3 bg-green-100 rounded-full">
                        <i class="fas fa-check-circle text-green-600 text-xl"></i>
                    </div>
                </div>
            </div>
        </div>
        <div class="card">
            <div class="card-body">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-600">总用户数</p>
                        <p class="text-2xl font-bold text-purple-600">1,247</p>
                    </div>
                    <div class="p-3 bg-purple-100 rounded-full">
                        <i class="fas fa-users text-purple-600 text-xl"></i>
                    </div>
                </div>
            </div>
        </div>
        <div class="card">
            <div class="card-body">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-600">存储使用量</p>
                        <p class="text-2xl font-bold text-orange-600">2.3TB</p>
                    </div>
                    <div class="p-3 bg-orange-100 rounded-full">
                        <i class="fas fa-database text-orange-600 text-xl"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 租户资源使用情况图表 -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
        <div class="card">
            <div class="card-body">
                <h3 class="text-lg font-semibold mb-4">租户资源使用分布</h3>
                <div class="chart-container">
                    <canvas id="resourceUsageChart"></canvas>
                </div>
            </div>
        </div>
        <div class="card">
            <div class="card-body">
                <h3 class="text-lg font-semibold mb-4">租户增长趋势</h3>
                <div class="chart-container">
                    <canvas id="tenantGrowthChart"></canvas>
                </div>
            </div>
        </div>
    </div>

    <!-- 租户列表 -->
    <div class="card">
        <div class="card-body">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg font-semibold">租户列表</h3>
                <div class="flex items-center space-x-4">
                    <div class="relative">
                        <input type="text" id="searchInput" placeholder="搜索租户..." 
                               class="form-input pl-10 pr-4 py-2 w-64">
                        <i class="fas fa-search absolute left-3 top-3 text-gray-400"></i>
                    </div>
                    <select id="statusFilter" class="form-select">
                        <option value="">全部状态</option>
                        <option value="active">活跃</option>
                        <option value="inactive">非活跃</option>
                        <option value="suspended">已暂停</option>
                    </select>
                    <select id="typeFilter" class="form-select">
                        <option value="">全部类型</option>
                        <option value="enterprise">企业版</option>
                        <option value="standard">标准版</option>
                        <option value="basic">基础版</option>
                    </select>
                </div>
            </div>
            
            <div class="overflow-x-auto">
                <table class="table" id="tenantTable">
                    <thead>
                        <tr>
                            <th>租户信息</th>
                            <th>类型</th>
                            <th>用户数</th>
                            <th>存储使用量</th>
                            <th>计算资源</th>
                            <th>状态</th>
                            <th>创建时间</th>
                            <th>到期时间</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody id="tenantTableBody">
                        <!-- 租户数据将通过JavaScript动态加载 -->
                    </tbody>
                </table>
            </div>
            
            <!-- 分页 -->
            <div class="flex items-center justify-between mt-4">
                <div class="text-sm text-gray-700">
                    显示 <span id="pageInfo">1-10</span> 条，共 <span id="totalCount">28</span> 条记录
                </div>
                <div class="flex items-center space-x-2">
                    <button onclick="changePage(-1)" class="btn btn-outline btn-sm">
                        <i class="fas fa-chevron-left"></i>
                    </button>
                    <span id="currentPage" class="px-3 py-1 bg-blue-100 text-blue-800 rounded">1</span>
                    <button onclick="changePage(1)" class="btn btn-outline btn-sm">
                        <i class="fas fa-chevron-right"></i>
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- 租户资源配额管理 -->
    <div class="card mt-6">
        <div class="card-body">
            <h3 class="text-lg font-semibold mb-4">资源配额管理</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                <div class="p-4 bg-blue-50 rounded-lg">
                    <div class="flex items-center justify-between mb-3">
                        <h4 class="font-medium text-blue-800">存储配额</h4>
                        <i class="fas fa-hdd text-blue-600"></i>
                    </div>
                    <div class="space-y-2">
                        <div class="flex justify-between text-sm">
                            <span>已分配</span>
                            <span class="font-medium">8.5TB / 10TB</span>
                        </div>
                        <div class="w-full bg-blue-200 rounded-full h-2">
                            <div class="bg-blue-600 h-2 rounded-full" style="width: 85%"></div>
                        </div>
                        <div class="text-xs text-blue-600">使用率: 85%</div>
                    </div>
                </div>
                
                <div class="p-4 bg-green-50 rounded-lg">
                    <div class="flex items-center justify-between mb-3">
                        <h4 class="font-medium text-green-800">计算配额</h4>
                        <i class="fas fa-microchip text-green-600"></i>
                    </div>
                    <div class="space-y-2">
                        <div class="flex justify-between text-sm">
                            <span>已分配</span>
                            <span class="font-medium">720 / 1000 vCPU</span>
                        </div>
                        <div class="w-full bg-green-200 rounded-full h-2">
                            <div class="bg-green-600 h-2 rounded-full" style="width: 72%"></div>
                        </div>
                        <div class="text-xs text-green-600">使用率: 72%</div>
                    </div>
                </div>
                
                <div class="p-4 bg-purple-50 rounded-lg">
                    <div class="flex items-center justify-between mb-3">
                        <h4 class="font-medium text-purple-800">内存配额</h4>
                        <i class="fas fa-memory text-purple-600"></i>
                    </div>
                    <div class="space-y-2">
                        <div class="flex justify-between text-sm">
                            <span>已分配</span>
                            <span class="font-medium">1.8TB / 2TB</span>
                        </div>
                        <div class="w-full bg-purple-200 rounded-full h-2">
                            <div class="bg-purple-600 h-2 rounded-full" style="width: 90%"></div>
                        </div>
                        <div class="text-xs text-purple-600">使用率: 90%</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        let currentPage = 1;
        const pageSize = 10;
        let tenantData = [];

        document.addEventListener('DOMContentLoaded', function() {
            initTenants();
        });

        function initTenants() {
            generateMockData();
            createCharts();
            renderTenantTable();
            
            // 搜索和筛选功能
            document.getElementById('searchInput').addEventListener('input', renderTenantTable);
            document.getElementById('statusFilter').addEventListener('change', renderTenantTable);
            document.getElementById('typeFilter').addEventListener('change', renderTenantTable);
        }

        function generateMockData() {
            const types = ['enterprise', 'standard', 'basic'];
            const typeLabels = { enterprise: '企业版', standard: '标准版', basic: '基础版' };
            const statuses = ['active', 'inactive', 'suspended'];
            const statusLabels = { active: '活跃', inactive: '非活跃', suspended: '已暂停' };
            const companies = [
                '中国矿产集团', '华北矿业', '西南资源', '东方矿产', '金山矿业',
                '蓝天资源', '绿地矿产', '红星集团', '银河矿业', '彩虹资源',
                '钢铁集团', '有色金属', '煤炭集团', '石油化工', '新能源矿产'
            ];

            for (let i = 1; i <= 28; i++) {
                const type = types[Math.floor(Math.random() * types.length)];
                const status = statuses[Math.floor(Math.random() * statuses.length)];
                const userCount = Math.floor(Math.random() * 100 + 10);
                const storageUsage = Math.floor(Math.random() * 500 + 50); // GB
                const cpuUsage = Math.floor(Math.random() * 50 + 5); // vCPU
                const company = companies[Math.floor(Math.random() * companies.length)];
                
                tenantData.push({
                    id: i,
                    name: `${company}_租户${i.toString().padStart(2, '0')}`,
                    company: company,
                    type: type,
                    typeLabel: typeLabels[type],
                    userCount: userCount,
                    storageUsage: storageUsage,
                    cpuUsage: cpuUsage,
                    status: status,
                    statusLabel: statusLabels[status],
                    createTime: new Date(Date.now() - Math.random() * 86400000 * 365).toLocaleString('zh-CN'),
                    expireTime: new Date(Date.now() + Math.random() * 86400000 * 365).toLocaleString('zh-CN'),
                    logo: `https://ui-avatars.com/api/?name=${encodeURIComponent(company)}&background=random&color=fff`
                });
            }
        }

        function createCharts() {
            // 租户资源使用分布图
            const usageCtx = document.getElementById('resourceUsageChart').getContext('2d');
            new Chart(usageCtx, {
                type: 'doughnut',
                data: {
                    labels: ['存储资源', '计算资源', '内存资源', '网络资源'],
                    datasets: [{
                        data: [35, 25, 30, 10],
                        backgroundColor: ['#3b82f6', '#10b981', '#8b5cf6', '#f59e0b']
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: { legend: { position: 'bottom' } }
                }
            });

            // 租户增长趋势图
            const growthCtx = document.getElementById('tenantGrowthChart').getContext('2d');
            new Chart(growthCtx, {
                type: 'line',
                data: {
                    labels: ['1月', '2月', '3月', '4月', '5月', '6月'],
                    datasets: [{
                        label: '新增租户',
                        data: [2, 4, 3, 5, 6, 4],
                        borderColor: 'rgb(59, 130, 246)',
                        backgroundColor: 'rgba(59, 130, 246, 0.1)',
                        tension: 0.4,
                        fill: true
                    }, {
                        label: '累计租户',
                        data: [18, 22, 25, 30, 36, 40],
                        borderColor: 'rgb(34, 197, 94)',
                        backgroundColor: 'rgba(34, 197, 94, 0.1)',
                        tension: 0.4
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: { legend: { position: 'bottom' } }
                }
            });
        }

        function renderTenantTable() {
            const searchTerm = document.getElementById('searchInput').value.toLowerCase();
            const statusFilter = document.getElementById('statusFilter').value;
            const typeFilter = document.getElementById('typeFilter').value;
            
            let filteredData = tenantData.filter(item => {
                const matchesSearch = item.name.toLowerCase().includes(searchTerm) || 
                                    item.company.toLowerCase().includes(searchTerm);
                const matchesStatus = !statusFilter || item.status === statusFilter;
                const matchesType = !typeFilter || item.type === typeFilter;
                return matchesSearch && matchesStatus && matchesType;
            });
            
            const startIndex = (currentPage - 1) * pageSize;
            const endIndex = startIndex + pageSize;
            const pageData = filteredData.slice(startIndex, endIndex);
            
            const tbody = document.getElementById('tenantTableBody');
            tbody.innerHTML = pageData.map(tenant => `
                <tr class="hover:bg-gray-50">
                    <td>
                        <div class="flex items-center space-x-3">
                            <img src="${tenant.logo}" alt="${tenant.company}" class="w-10 h-10 rounded-full">
                            <div>
                                <div class="font-medium text-gray-900">${tenant.name}</div>
                                <div class="text-sm text-gray-500">${tenant.company}</div>
                            </div>
                        </div>
                    </td>
                    <td>
                        <span class="px-2 py-1 bg-blue-100 text-blue-800 rounded text-sm">${tenant.typeLabel}</span>
                    </td>
                    <td class="text-center">
                        <div class="font-medium">${tenant.userCount}</div>
                        <div class="text-sm text-gray-500">用户</div>
                    </td>
                    <td>
                        <div class="font-medium">${tenant.storageUsage}GB</div>
                        <div class="w-16 bg-gray-200 rounded-full h-1 mt-1">
                            <div class="bg-blue-600 h-1 rounded-full" style="width: ${Math.min(tenant.storageUsage / 10, 100)}%"></div>
                        </div>
                    </td>
                    <td>
                        <div class="font-medium">${tenant.cpuUsage} vCPU</div>
                        <div class="w-16 bg-gray-200 rounded-full h-1 mt-1">
                            <div class="bg-green-600 h-1 rounded-full" style="width: ${Math.min(tenant.cpuUsage * 2, 100)}%"></div>
                        </div>
                    </td>
                    <td>
                        <span class="status-badge ${getStatusClass(tenant.status)}">${tenant.statusLabel}</span>
                    </td>
                    <td class="text-sm text-gray-500">${tenant.createTime}</td>
                    <td class="text-sm text-gray-500">${tenant.expireTime}</td>
                    <td>
                        <div class="flex items-center space-x-2">
                            <button onclick="viewTenant(${tenant.id})" class="text-blue-600 hover:text-blue-800" title="查看详情">
                                <i class="fas fa-eye"></i>
                            </button>
                            <button onclick="editTenant(${tenant.id})" class="text-green-600 hover:text-green-800" title="编辑">
                                <i class="fas fa-edit"></i>
                            </button>
                            <button onclick="manageTenantUsers(${tenant.id})" class="text-purple-600 hover:text-purple-800" title="用户管理">
                                <i class="fas fa-users"></i>
                            </button>
                            <button onclick="manageTenantResources(${tenant.id})" class="text-orange-600 hover:text-orange-800" title="资源管理">
                                <i class="fas fa-cogs"></i>
                            </button>
                            ${tenant.status === 'active' ? 
                                `<button onclick="suspendTenant(${tenant.id})" class="text-red-600 hover:text-red-800" title="暂停">
                                    <i class="fas fa-pause"></i>
                                </button>` :
                                `<button onclick="activateTenant(${tenant.id})" class="text-green-600 hover:text-green-800" title="激活">
                                    <i class="fas fa-play"></i>
                                </button>`
                            }
                        </div>
                    </td>
                </tr>
            `).join('');
            
            // 更新分页信息
            document.getElementById('pageInfo').textContent = `${startIndex + 1}-${Math.min(endIndex, filteredData.length)}`;
            document.getElementById('totalCount').textContent = filteredData.length;
            document.getElementById('currentPage').textContent = currentPage;
        }

        function getStatusClass(status) {
            const classes = {
                active: 'status-success',
                inactive: 'status-warning',
                suspended: 'status-error'
            };
            return classes[status] || 'status-info';
        }

        function changePage(direction) {
            const newPage = currentPage + direction;
            if (newPage >= 1 && newPage <= Math.ceil(tenantData.length / pageSize)) {
                currentPage = newPage;
                renderTenantTable();
            }
        }

        function showCreateTenantModal() {
            if (parent.showModal) {
                parent.showModal('创建租户', `
                    <form class="space-y-4">
                        <div class="grid grid-cols-2 gap-4">
                            <div class="form-group">
                                <label class="form-label">租户名称</label>
                                <input type="text" class="form-input" required>
                            </div>
                            <div class="form-group">
                                <label class="form-label">公司名称</label>
                                <input type="text" class="form-input" required>
                            </div>
                        </div>
                        <div class="grid grid-cols-2 gap-4">
                            <div class="form-group">
                                <label class="form-label">租户类型</label>
                                <select class="form-select" required>
                                    <option value="">请选择</option>
                                    <option value="enterprise">企业版</option>
                                    <option value="standard">标准版</option>
                                    <option value="basic">基础版</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label class="form-label">联系邮箱</label>
                                <input type="email" class="form-input" required>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="form-label">租户描述</label>
                            <textarea class="form-input" rows="3"></textarea>
                        </div>
                        <div class="grid grid-cols-3 gap-4">
                            <div class="form-group">
                                <label class="form-label">存储配额(GB)</label>
                                <input type="number" class="form-input" value="1000" required>
                            </div>
                            <div class="form-group">
                                <label class="form-label">计算配额(vCPU)</label>
                                <input type="number" class="form-input" value="100" required>
                            </div>
                            <div class="form-group">
                                <label class="form-label">用户数限制</label>
                                <input type="number" class="form-input" value="50" required>
                            </div>
                        </div>
                        <div class="grid grid-cols-2 gap-4">
                            <div class="form-group">
                                <label class="form-label">生效时间</label>
                                <input type="datetime-local" class="form-input" required>
                            </div>
                            <div class="form-group">
                                <label class="form-label">到期时间</label>
                                <input type="datetime-local" class="form-input" required>
                            </div>
                        </div>
                    </form>
                `, [
                    { text: '取消', class: 'btn-outline' },
                    { text: '创建', class: 'btn-primary', onclick: 'createTenant()' }
                ]);
            }
        }

        function createTenant() {
            if (parent.showToast) {
                parent.showToast('租户创建成功', 'success');
            }
        }

        function viewTenant(id) {
            const tenant = tenantData.find(item => item.id === id);
            if (parent.showModal && tenant) {
                parent.showModal('租户详情', `
                    <div class="space-y-4">
                        <div class="flex items-center space-x-4">
                            <img src="${tenant.logo}" alt="${tenant.company}" class="w-16 h-16 rounded-full">
                            <div>
                                <h4 class="text-lg font-semibold">${tenant.name}</h4>
                                <p class="text-gray-600">${tenant.company}</p>
                            </div>
                        </div>
                        <div class="grid grid-cols-2 gap-4 text-sm">
                            <div><strong>租户类型：</strong>${tenant.typeLabel}</div>
                            <div><strong>用户数量：</strong>${tenant.userCount}</div>
                            <div><strong>存储使用：</strong>${tenant.storageUsage}GB</div>
                            <div><strong>计算资源：</strong>${tenant.cpuUsage} vCPU</div>
                            <div><strong>状态：</strong><span class="status-badge ${getStatusClass(tenant.status)}">${tenant.statusLabel}</span></div>
                            <div><strong>创建时间：</strong>${tenant.createTime}</div>
                            <div><strong>到期时间：</strong>${tenant.expireTime}</div>
                        </div>
                    </div>
                `);
            }
        }

        function editTenant(id) {
            if (parent.showToast) {
                parent.showToast('编辑功能开发中', 'info');
            }
        }

        function manageTenantUsers(id) {
            if (parent.showToast) {
                parent.showToast('正在跳转到用户管理页面...', 'info');
            }
        }

        function manageTenantResources(id) {
            if (parent.showModal) {
                parent.showModal('资源配额管理', `
                    <form class="space-y-4">
                        <div class="grid grid-cols-2 gap-4">
                            <div class="form-group">
                                <label class="form-label">存储配额(GB)</label>
                                <input type="number" class="form-input" value="1000">
                            </div>
                            <div class="form-group">
                                <label class="form-label">计算配额(vCPU)</label>
                                <input type="number" class="form-input" value="100">
                            </div>
                        </div>
                        <div class="grid grid-cols-2 gap-4">
                            <div class="form-group">
                                <label class="form-label">内存配额(GB)</label>
                                <input type="number" class="form-input" value="500">
                            </div>
                            <div class="form-group">
                                <label class="form-label">网络带宽(Mbps)</label>
                                <input type="number" class="form-input" value="1000">
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="form-label">用户数限制</label>
                            <input type="number" class="form-input" value="50">
                        </div>
                    </form>
                `, [
                    { text: '取消', class: 'btn-outline' },
                    { text: '保存', class: 'btn-primary', onclick: 'updateTenantResources()' }
                ]);
            }
        }

        function updateTenantResources() {
            if (parent.showToast) {
                parent.showToast('资源配额更新成功', 'success');
            }
        }

        function suspendTenant(id) {
            if (parent.showConfirm) {
                parent.showConfirm('确定要暂停这个租户吗？暂停后该租户将无法访问系统。', function() {
                    if (parent.showToast) {
                        parent.showToast('租户已暂停', 'warning');
                    }
                });
            }
        }

        function activateTenant(id) {
            if (parent.showConfirm) {
                parent.showConfirm('确定要激活这个租户吗？', function() {
                    if (parent.showToast) {
                        parent.showToast('租户已激活', 'success');
                    }
                });
            }
        }

        function exportTenants() {
            if (parent.showToast) {
                parent.showToast('租户数据导出功能开发中', 'info');
            }
        }
    </script>
</body>
</html>
