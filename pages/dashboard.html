<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>首页仪表板</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <link rel="stylesheet" href="../css/style.css">
</head>
<body class="bg-gray-100 p-6">
    <!-- 页面头部 -->
    <div class="mb-6">
        <div class="flex items-center justify-between">
            <div>
                <h1 class="text-2xl font-bold text-gray-900">首页仪表板</h1>
                <p class="text-gray-600 mt-1">战略性矿产资源数据运营服务平台总览</p>
            </div>
            <div class="flex items-center space-x-4">
                <div class="text-sm text-gray-500">
                    <i class="fas fa-clock mr-1"></i>
                    最后更新：<span id="lastUpdate">2024-01-15 14:30:25</span>
                </div>
                <button onclick="refreshDashboard()" class="btn btn-primary">
                    <i class="fas fa-sync-alt mr-2"></i>刷新数据
                </button>
            </div>
        </div>
    </div>

    <!-- 统计卡片 -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <div class="stat-card bg-gradient-to-r from-blue-500 to-blue-600">
            <div class="stat-value">2.5TB</div>
            <div class="stat-label">总数据存储量</div>
            <i class="fas fa-database stat-icon"></i>
        </div>
        <div class="stat-card bg-gradient-to-r from-green-500 to-green-600">
            <div class="stat-value">1,247</div>
            <div class="stat-label">活跃数据源</div>
            <i class="fas fa-plug stat-icon"></i>
        </div>
        <div class="stat-card bg-gradient-to-r from-purple-500 to-purple-600">
            <div class="stat-value">89.5%</div>
            <div class="stat-label">数据质量评分</div>
            <i class="fas fa-chart-line stat-icon"></i>
        </div>
        <div class="stat-card bg-gradient-to-r from-orange-500 to-orange-600">
            <div class="stat-value">156</div>
            <div class="stat-label">在线用户数</div>
            <i class="fas fa-users stat-icon"></i>
        </div>
    </div>

    <!-- 图表区域 -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
        <!-- 数据增长趋势 -->
        <div class="card">
            <div class="card-body">
                <h3 class="text-lg font-semibold mb-4">数据增长趋势</h3>
                <div class="chart-container">
                    <canvas id="dataGrowthChart"></canvas>
                </div>
            </div>
        </div>

        <!-- 系统资源使用率 -->
        <div class="card">
            <div class="card-body">
                <h3 class="text-lg font-semibold mb-4">系统资源使用率</h3>
                <div class="chart-container">
                    <canvas id="resourceChart"></canvas>
                </div>
            </div>
        </div>
    </div>

    <!-- 数据源状态和任务执行情况 -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
        <!-- 数据源状态 -->
        <div class="card">
            <div class="card-body">
                <h3 class="text-lg font-semibold mb-4">数据源状态</h3>
                <div class="space-y-3">
                    <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                        <div class="flex items-center">
                            <div class="w-3 h-3 bg-green-500 rounded-full mr-3"></div>
                            <span class="font-medium">MySQL数据库</span>
                        </div>
                        <span class="text-sm text-gray-500">正常运行</span>
                    </div>
                    <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                        <div class="flex items-center">
                            <div class="w-3 h-3 bg-green-500 rounded-full mr-3"></div>
                            <span class="font-medium">Oracle数据库</span>
                        </div>
                        <span class="text-sm text-gray-500">正常运行</span>
                    </div>
                    <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                        <div class="flex items-center">
                            <div class="w-3 h-3 bg-yellow-500 rounded-full mr-3"></div>
                            <span class="font-medium">Kafka消息队列</span>
                        </div>
                        <span class="text-sm text-gray-500">连接缓慢</span>
                    </div>
                    <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                        <div class="flex items-center">
                            <div class="w-3 h-3 bg-red-500 rounded-full mr-3"></div>
                            <span class="font-medium">文件服务器</span>
                        </div>
                        <span class="text-sm text-gray-500">连接异常</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- 最近任务执行情况 -->
        <div class="card">
            <div class="card-body">
                <h3 class="text-lg font-semibold mb-4">最近任务执行情况</h3>
                <div class="space-y-3">
                    <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                        <div>
                            <div class="font-medium">矿产数据同步任务</div>
                            <div class="text-sm text-gray-500">2024-01-15 14:25:00</div>
                        </div>
                        <span class="status-badge status-success">成功</span>
                    </div>
                    <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                        <div>
                            <div class="font-medium">数据质量检查</div>
                            <div class="text-sm text-gray-500">2024-01-15 14:20:00</div>
                        </div>
                        <span class="status-badge status-success">成功</span>
                    </div>
                    <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                        <div>
                            <div class="font-medium">报表生成任务</div>
                            <div class="text-sm text-gray-500">2024-01-15 14:15:00</div>
                        </div>
                        <span class="status-badge status-warning">执行中</span>
                    </div>
                    <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                        <div>
                            <div class="font-medium">数据备份任务</div>
                            <div class="text-sm text-gray-500">2024-01-15 14:10:00</div>
                        </div>
                        <span class="status-badge status-error">失败</span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 快速操作和系统公告 -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <!-- 快速操作 -->
        <div class="card">
            <div class="card-body">
                <h3 class="text-lg font-semibold mb-4">快速操作</h3>
                <div class="grid grid-cols-2 gap-4">
                    <button onclick="parent.loadPage('pages/integration.html')" class="flex flex-col items-center p-4 bg-blue-50 rounded-lg hover:bg-blue-100 transition-colors">
                        <i class="fas fa-exchange-alt text-2xl text-blue-600 mb-2"></i>
                        <span class="text-sm font-medium">数据集成</span>
                    </button>
                    <button onclick="parent.loadPage('pages/quality.html')" class="flex flex-col items-center p-4 bg-green-50 rounded-lg hover:bg-green-100 transition-colors">
                        <i class="fas fa-check-circle text-2xl text-green-600 mb-2"></i>
                        <span class="text-sm font-medium">质量检查</span>
                    </button>
                    <button onclick="parent.loadPage('pages/reports.html')" class="flex flex-col items-center p-4 bg-purple-50 rounded-lg hover:bg-purple-100 transition-colors">
                        <i class="fas fa-chart-bar text-2xl text-purple-600 mb-2"></i>
                        <span class="text-sm font-medium">报表分析</span>
                    </button>
                    <button onclick="parent.loadPage('pages/users.html')" class="flex flex-col items-center p-4 bg-orange-50 rounded-lg hover:bg-orange-100 transition-colors">
                        <i class="fas fa-users text-2xl text-orange-600 mb-2"></i>
                        <span class="text-sm font-medium">用户管理</span>
                    </button>
                </div>
            </div>
        </div>

        <!-- 系统公告 -->
        <div class="card">
            <div class="card-body">
                <h3 class="text-lg font-semibold mb-4">系统公告</h3>
                <div class="space-y-3">
                    <div class="p-3 bg-blue-50 rounded-lg border-l-4 border-blue-500">
                        <div class="font-medium text-blue-800">系统维护通知</div>
                        <div class="text-sm text-blue-600 mt-1">系统将于本周六凌晨2:00-4:00进行维护升级</div>
                        <div class="text-xs text-blue-500 mt-2">2024-01-15</div>
                    </div>
                    <div class="p-3 bg-green-50 rounded-lg border-l-4 border-green-500">
                        <div class="font-medium text-green-800">新功能上线</div>
                        <div class="text-sm text-green-600 mt-1">智能数据分析功能已正式上线，欢迎体验</div>
                        <div class="text-xs text-green-500 mt-2">2024-01-14</div>
                    </div>
                    <div class="p-3 bg-yellow-50 rounded-lg border-l-4 border-yellow-500">
                        <div class="font-medium text-yellow-800">安全提醒</div>
                        <div class="text-sm text-yellow-600 mt-1">请定期修改密码，确保账户安全</div>
                        <div class="text-xs text-yellow-500 mt-2">2024-01-13</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            initDashboard();
        });

        function initDashboard() {
            createDataGrowthChart();
            createResourceChart();
            updateLastUpdateTime();
        }

        function createDataGrowthChart() {
            const ctx = document.getElementById('dataGrowthChart').getContext('2d');
            new Chart(ctx, {
                type: 'line',
                data: {
                    labels: ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月'],
                    datasets: [{
                        label: '数据量(TB)',
                        data: [0.5, 0.8, 1.2, 1.5, 1.8, 2.0, 2.2, 2.3, 2.4, 2.45, 2.48, 2.5],
                        borderColor: 'rgb(59, 130, 246)',
                        backgroundColor: 'rgba(59, 130, 246, 0.1)',
                        tension: 0.4,
                        fill: true
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            ticks: {
                                callback: function(value) {
                                    return value + 'TB';
                                }
                            }
                        }
                    }
                }
            });
        }

        function createResourceChart() {
            const ctx = document.getElementById('resourceChart').getContext('2d');
            new Chart(ctx, {
                type: 'doughnut',
                data: {
                    labels: ['CPU使用率', 'CPU空闲', '内存使用率', '内存空闲', '存储使用率', '存储空闲'],
                    datasets: [{
                        data: [65, 35, 78, 22, 45, 55],
                        backgroundColor: [
                            '#ef4444',
                            '#fee2e2',
                            '#f59e0b',
                            '#fef3c7',
                            '#10b981',
                            '#dcfce7'
                        ]
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'bottom'
                        }
                    }
                }
            });
        }

        function updateLastUpdateTime() {
            const now = new Date();
            const timeString = now.toLocaleString('zh-CN');
            document.getElementById('lastUpdate').textContent = timeString;
        }

        function refreshDashboard() {
            // 模拟刷新数据
            const button = event.target.closest('button');
            const originalText = button.innerHTML;
            
            button.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>刷新中...';
            button.disabled = true;
            
            setTimeout(() => {
                updateLastUpdateTime();
                button.innerHTML = originalText;
                button.disabled = false;
                
                // 显示成功提示
                if (parent.showToast) {
                    parent.showToast('数据刷新成功', 'success');
                }
            }, 2000);
        }
    </script>
</body>
</html>
