<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>元数据管理</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="../css/style.css">
</head>
<body class="bg-gray-100 p-6">
    <!-- 页面头部 -->
    <div class="mb-6">
        <div class="flex items-center justify-between">
            <div>
                <h1 class="text-2xl font-bold text-gray-900">元数据管理</h1>
                <p class="text-gray-600 mt-1">统一管理多源数据的元数据信息</p>
            </div>
            <div class="flex items-center space-x-4">
                <button onclick="syncMetadata()" class="btn btn-primary">
                    <i class="fas fa-sync-alt mr-2"></i>同步元数据
                </button>
                <button onclick="showAddDataSourceModal()" class="btn btn-outline">
                    <i class="fas fa-plus mr-2"></i>添加数据源
                </button>
            </div>
        </div>
    </div>

    <!-- 元数据统计 -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <div class="card">
            <div class="card-body">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-600">数据源总数</p>
                        <p class="text-2xl font-bold text-gray-900">47</p>
                    </div>
                    <div class="p-3 bg-blue-100 rounded-full">
                        <i class="fas fa-database text-blue-600 text-xl"></i>
                    </div>
                </div>
            </div>
        </div>
        <div class="card">
            <div class="card-body">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-600">数据表总数</p>
                        <p class="text-2xl font-bold text-gray-900">1,247</p>
                    </div>
                    <div class="p-3 bg-green-100 rounded-full">
                        <i class="fas fa-table text-green-600 text-xl"></i>
                    </div>
                </div>
            </div>
        </div>
        <div class="card">
            <div class="card-body">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-600">字段总数</p>
                        <p class="text-2xl font-bold text-gray-900">15,632</p>
                    </div>
                    <div class="p-3 bg-yellow-100 rounded-full">
                        <i class="fas fa-columns text-yellow-600 text-xl"></i>
                    </div>
                </div>
            </div>
        </div>
        <div class="card">
            <div class="card-body">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-600">同步状态</p>
                        <p class="text-2xl font-bold text-green-600">正常</p>
                    </div>
                    <div class="p-3 bg-purple-100 rounded-full">
                        <i class="fas fa-check-circle text-purple-600 text-xl"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <!-- 数据源树形结构 -->
        <div class="card">
            <div class="card-body">
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-lg font-semibold">数据源目录</h3>
                    <button onclick="expandAll()" class="text-sm text-blue-600 hover:text-blue-800">
                        <i class="fas fa-expand-arrows-alt mr-1"></i>展开全部
                    </button>
                </div>
                
                <div class="space-y-2" id="dataSourceTree">
                    <!-- 数据源树将通过JavaScript动态生成 -->
                </div>
            </div>
        </div>

        <!-- 元数据详情 -->
        <div class="lg:col-span-2">
            <div class="card">
                <div class="card-body">
                    <div class="flex items-center justify-between mb-4">
                        <h3 class="text-lg font-semibold">元数据详情</h3>
                        <div class="flex items-center space-x-2">
                            <input type="text" id="searchMetadata" placeholder="搜索表或字段..." 
                                   class="form-input w-64">
                            <button onclick="exportMetadata()" class="btn btn-outline btn-sm">
                                <i class="fas fa-download mr-1"></i>导出
                            </button>
                        </div>
                    </div>
                    
                    <div id="metadataContent">
                        <div class="text-center py-12 text-gray-500">
                            <i class="fas fa-mouse-pointer text-4xl mb-4"></i>
                            <p>请从左侧选择数据源查看详细信息</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 最近同步记录 -->
    <div class="card mt-6">
        <div class="card-body">
            <h3 class="text-lg font-semibold mb-4">最近同步记录</h3>
            <div class="overflow-x-auto">
                <table class="table">
                    <thead>
                        <tr>
                            <th>数据源</th>
                            <th>同步类型</th>
                            <th>同步时间</th>
                            <th>状态</th>
                            <th>新增表</th>
                            <th>更新表</th>
                            <th>删除表</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody id="syncRecordsBody">
                        <!-- 同步记录将通过JavaScript动态加载 -->
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <script>
        let selectedDataSource = null;
        let metadataData = {};

        document.addEventListener('DOMContentLoaded', function() {
            initMetadata();
        });

        function initMetadata() {
            generateMockData();
            renderDataSourceTree();
            renderSyncRecords();
            
            // 搜索功能
            document.getElementById('searchMetadata').addEventListener('input', function() {
                if (selectedDataSource) {
                    renderMetadataDetails(selectedDataSource);
                }
            });
        }

        function generateMockData() {
            const dataSources = [
                { id: 'mysql_prod', name: 'MySQL生产库', type: 'MySQL', status: 'online' },
                { id: 'oracle_dwh', name: 'Oracle数据仓库', type: 'Oracle', status: 'online' },
                { id: 'hive_lake', name: 'Hive数据湖', type: 'Hive', status: 'online' },
                { id: 'kafka_stream', name: 'Kafka消息流', type: 'Kafka', status: 'warning' },
                { id: 'file_server', name: '文件服务器', type: 'File', status: 'offline' }
            ];

            dataSources.forEach(ds => {
                metadataData[ds.id] = {
                    ...ds,
                    tables: generateMockTables(ds.id)
                };
            });
        }

        function generateMockTables(dataSourceId) {
            const tableNames = [
                'mineral_resources', 'mining_permits', 'production_data', 'quality_reports',
                'environmental_data', 'safety_records', 'equipment_info', 'personnel_info'
            ];
            
            return tableNames.map((name, index) => ({
                id: `${dataSourceId}_${name}`,
                name: name,
                comment: `${name.replace(/_/g, ' ')}表`,
                rowCount: Math.floor(Math.random() * 100000 + 1000),
                columns: generateMockColumns(name),
                lastUpdate: new Date(Date.now() - Math.random() * 86400000 * 7).toLocaleString('zh-CN')
            }));
        }

        function generateMockColumns(tableName) {
            const commonColumns = ['id', 'created_at', 'updated_at', 'status'];
            const specificColumns = {
                mineral_resources: ['mineral_type', 'location', 'reserves', 'grade'],
                mining_permits: ['permit_number', 'company_name', 'valid_from', 'valid_to'],
                production_data: ['production_date', 'output_volume', 'quality_grade'],
                quality_reports: ['test_date', 'sample_id', 'test_results', 'inspector']
            };
            
            const columns = [...commonColumns, ...(specificColumns[tableName] || ['data_field'])];
            
            return columns.map(col => ({
                name: col,
                type: getColumnType(col),
                nullable: col !== 'id',
                comment: `${col}字段说明`
            }));
        }

        function getColumnType(columnName) {
            if (columnName === 'id') return 'BIGINT';
            if (columnName.includes('date') || columnName.includes('time')) return 'DATETIME';
            if (columnName.includes('number') || columnName.includes('count')) return 'INT';
            if (columnName.includes('volume') || columnName.includes('grade')) return 'DECIMAL';
            return 'VARCHAR(255)';
        }

        function renderDataSourceTree() {
            const treeContainer = document.getElementById('dataSourceTree');
            const html = Object.values(metadataData).map(ds => `
                <div class="data-source-item">
                    <div class="flex items-center p-2 hover:bg-gray-50 rounded cursor-pointer" 
                         onclick="toggleDataSource('${ds.id}')">
                        <i class="fas fa-chevron-right transform transition-transform mr-2" id="chevron-${ds.id}"></i>
                        <i class="fas fa-database text-blue-600 mr-2"></i>
                        <span class="font-medium">${ds.name}</span>
                        <span class="ml-auto">
                            <span class="status-badge ${getStatusClass(ds.status)}">${getStatusLabel(ds.status)}</span>
                        </span>
                    </div>
                    <div class="ml-6 hidden" id="tables-${ds.id}">
                        ${ds.tables.map(table => `
                            <div class="flex items-center p-2 hover:bg-gray-50 rounded cursor-pointer"
                                 onclick="selectTable('${ds.id}', '${table.id}')">
                                <i class="fas fa-table text-green-600 mr-2"></i>
                                <span>${table.name}</span>
                                <span class="ml-auto text-sm text-gray-500">${table.rowCount}行</span>
                            </div>
                        `).join('')}
                    </div>
                </div>
            `).join('');
            
            treeContainer.innerHTML = html;
        }

        function toggleDataSource(dataSourceId) {
            const chevron = document.getElementById(`chevron-${dataSourceId}`);
            const tables = document.getElementById(`tables-${dataSourceId}`);
            
            if (tables.classList.contains('hidden')) {
                tables.classList.remove('hidden');
                chevron.style.transform = 'rotate(90deg)';
            } else {
                tables.classList.add('hidden');
                chevron.style.transform = 'rotate(0deg)';
            }
        }

        function selectTable(dataSourceId, tableId) {
            selectedDataSource = dataSourceId;
            const table = metadataData[dataSourceId].tables.find(t => t.id === tableId);
            renderMetadataDetails(dataSourceId, table);
        }

        function renderMetadataDetails(dataSourceId, table = null) {
            const content = document.getElementById('metadataContent');
            const searchTerm = document.getElementById('searchMetadata').value.toLowerCase();
            
            if (!table) {
                // 显示数据源概览
                const ds = metadataData[dataSourceId];
                content.innerHTML = `
                    <div class="space-y-6">
                        <div class="border-b pb-4">
                            <h4 class="text-xl font-semibold mb-2">${ds.name}</h4>
                            <div class="grid grid-cols-2 gap-4 text-sm">
                                <div><strong>类型：</strong>${ds.type}</div>
                                <div><strong>状态：</strong><span class="status-badge ${getStatusClass(ds.status)}">${getStatusLabel(ds.status)}</span></div>
                                <div><strong>表数量：</strong>${ds.tables.length}</div>
                                <div><strong>总记录数：</strong>${ds.tables.reduce((sum, t) => sum + t.rowCount, 0).toLocaleString()}</div>
                            </div>
                        </div>
                        <div>
                            <h5 class="font-semibold mb-3">数据表列表</h5>
                            <div class="space-y-2">
                                ${ds.tables.filter(t => !searchTerm || t.name.toLowerCase().includes(searchTerm)).map(t => `
                                    <div class="flex items-center justify-between p-3 bg-gray-50 rounded">
                                        <div>
                                            <div class="font-medium">${t.name}</div>
                                            <div class="text-sm text-gray-500">${t.comment}</div>
                                        </div>
                                        <div class="text-right text-sm">
                                            <div>${t.rowCount.toLocaleString()}行</div>
                                            <div class="text-gray-500">${t.columns.length}列</div>
                                        </div>
                                    </div>
                                `).join('')}
                            </div>
                        </div>
                    </div>
                `;
            } else {
                // 显示表详情
                const filteredColumns = table.columns.filter(col => 
                    !searchTerm || col.name.toLowerCase().includes(searchTerm) || 
                    col.comment.toLowerCase().includes(searchTerm)
                );
                
                content.innerHTML = `
                    <div class="space-y-6">
                        <div class="border-b pb-4">
                            <h4 class="text-xl font-semibold mb-2">${table.name}</h4>
                            <p class="text-gray-600 mb-3">${table.comment}</p>
                            <div class="grid grid-cols-3 gap-4 text-sm">
                                <div><strong>记录数：</strong>${table.rowCount.toLocaleString()}</div>
                                <div><strong>字段数：</strong>${table.columns.length}</div>
                                <div><strong>最后更新：</strong>${table.lastUpdate}</div>
                            </div>
                        </div>
                        <div>
                            <h5 class="font-semibold mb-3">字段信息</h5>
                            <div class="overflow-x-auto">
                                <table class="table">
                                    <thead>
                                        <tr>
                                            <th>字段名</th>
                                            <th>数据类型</th>
                                            <th>允许空值</th>
                                            <th>说明</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        ${filteredColumns.map(col => `
                                            <tr>
                                                <td class="font-medium">${col.name}</td>
                                                <td><span class="px-2 py-1 bg-blue-100 text-blue-800 rounded text-sm">${col.type}</span></td>
                                                <td>${col.nullable ? '是' : '否'}</td>
                                                <td class="text-gray-600">${col.comment}</td>
                                            </tr>
                                        `).join('')}
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                `;
            }
        }

        function renderSyncRecords() {
            const tbody = document.getElementById('syncRecordsBody');
            const records = [
                { dataSource: 'MySQL生产库', type: '增量同步', time: '2024-01-15 14:30:00', status: 'success', added: 5, updated: 12, deleted: 0 },
                { dataSource: 'Oracle数据仓库', type: '全量同步', time: '2024-01-15 14:25:00', status: 'success', added: 0, updated: 8, deleted: 2 },
                { dataSource: 'Hive数据湖', type: '增量同步', time: '2024-01-15 14:20:00', status: 'warning', added: 3, updated: 5, deleted: 1 },
                { dataSource: 'Kafka消息流', type: '实时同步', time: '2024-01-15 14:15:00', status: 'error', added: 0, updated: 0, deleted: 0 }
            ];
            
            tbody.innerHTML = records.map(record => `
                <tr>
                    <td class="font-medium">${record.dataSource}</td>
                    <td><span class="px-2 py-1 bg-gray-100 rounded text-sm">${record.type}</span></td>
                    <td class="text-sm text-gray-500">${record.time}</td>
                    <td><span class="status-badge ${getStatusClass(record.status)}">${getStatusLabel(record.status)}</span></td>
                    <td class="text-center">${record.added}</td>
                    <td class="text-center">${record.updated}</td>
                    <td class="text-center">${record.deleted}</td>
                    <td>
                        <button onclick="viewSyncDetails('${record.dataSource}')" class="text-blue-600 hover:text-blue-800">
                            <i class="fas fa-eye"></i>
                        </button>
                    </td>
                </tr>
            `).join('');
        }

        function getStatusClass(status) {
            const classes = {
                online: 'status-success',
                success: 'status-success',
                warning: 'status-warning',
                offline: 'status-error',
                error: 'status-error'
            };
            return classes[status] || 'status-info';
        }

        function getStatusLabel(status) {
            const labels = {
                online: '在线',
                success: '成功',
                warning: '警告',
                offline: '离线',
                error: '失败'
            };
            return labels[status] || status;
        }

        function expandAll() {
            Object.keys(metadataData).forEach(dsId => {
                const tables = document.getElementById(`tables-${dsId}`);
                const chevron = document.getElementById(`chevron-${dsId}`);
                if (tables && chevron) {
                    tables.classList.remove('hidden');
                    chevron.style.transform = 'rotate(90deg)';
                }
            });
        }

        function syncMetadata() {
            if (parent.showToast) {
                parent.showToast('元数据同步已启动', 'info');
                setTimeout(() => {
                    parent.showToast('元数据同步完成', 'success');
                }, 3000);
            }
        }

        function showAddDataSourceModal() {
            if (parent.showModal) {
                parent.showModal('添加数据源', `
                    <form class="space-y-4">
                        <div class="form-group">
                            <label class="form-label">数据源名称</label>
                            <input type="text" class="form-input" required>
                        </div>
                        <div class="form-group">
                            <label class="form-label">数据源类型</label>
                            <select class="form-select" required>
                                <option value="">请选择</option>
                                <option value="MySQL">MySQL</option>
                                <option value="Oracle">Oracle</option>
                                <option value="PostgreSQL">PostgreSQL</option>
                                <option value="Hive">Hive</option>
                                <option value="Kafka">Kafka</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label class="form-label">连接地址</label>
                            <input type="text" class="form-input" required>
                        </div>
                        <div class="form-group">
                            <label class="form-label">端口</label>
                            <input type="number" class="form-input" required>
                        </div>
                    </form>
                `, [
                    { text: '取消', class: 'btn-outline' },
                    { text: '添加', class: 'btn-primary', onclick: 'addDataSource()' }
                ]);
            }
        }

        function addDataSource() {
            if (parent.showToast) {
                parent.showToast('数据源添加成功', 'success');
            }
        }

        function exportMetadata() {
            if (parent.showToast) {
                parent.showToast('元数据导出功能开发中', 'info');
            }
        }

        function viewSyncDetails(dataSource) {
            if (parent.showModal) {
                parent.showModal('同步详情', `
                    <div class="space-y-3">
                        <div><strong>数据源：</strong>${dataSource}</div>
                        <div><strong>同步状态：</strong>成功</div>
                        <div><strong>开始时间：</strong>2024-01-15 14:30:00</div>
                        <div><strong>结束时间：</strong>2024-01-15 14:32:15</div>
                        <div><strong>耗时：</strong>2分15秒</div>
                        <div><strong>处理记录：</strong>1,247条</div>
                    </div>
                `);
            }
        }
    </script>
</body>
</html>
