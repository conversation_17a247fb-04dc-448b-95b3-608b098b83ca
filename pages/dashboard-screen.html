<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>数据大屏</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <link rel="stylesheet" href="../css/style.css">
    <style>
        body {
            background: linear-gradient(135deg, #0f172a 0%, #1e293b 100%);
            color: white;
            overflow: hidden;
        }
        .screen-card {
            background: rgba(30, 41, 59, 0.8);
            border: 1px solid rgba(59, 130, 246, 0.3);
            backdrop-filter: blur(10px);
        }
        .glow-text {
            text-shadow: 0 0 10px rgba(59, 130, 246, 0.5);
        }
        .data-number {
            font-family: 'Courier New', monospace;
            font-weight: bold;
        }
        .pulse-dot {
            animation: pulse 2s infinite;
        }
        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }
        .chart-container {
            height: 200px;
        }
        .large-chart {
            height: 300px;
        }
        .status-indicator {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            display: inline-block;
            margin-right: 8px;
        }
        .status-online { background-color: #10b981; }
        .status-warning { background-color: #f59e0b; }
        .status-offline { background-color: #ef4444; }
    </style>
</head>
<body class="p-4">
    <!-- 顶部标题栏 -->
    <div class="text-center mb-6">
        <h1 class="text-4xl font-bold glow-text mb-2">战略性矿产资源数据运营服务平台</h1>
        <div class="flex items-center justify-center space-x-8 text-lg">
            <div class="flex items-center">
                <span class="status-indicator status-online pulse-dot"></span>
                <span>系统运行正常</span>
            </div>
            <div>当前时间：<span id="currentTime" class="data-number"></span></div>
            <div>在线用户：<span class="data-number text-green-400">298</span></div>
        </div>
    </div>

    <!-- 主要数据指标 -->
    <div class="grid grid-cols-6 gap-4 mb-6">
        <div class="screen-card rounded-lg p-4 text-center">
            <div class="text-3xl data-number text-blue-400 mb-2">2.3TB</div>
            <div class="text-sm text-gray-300">数据总量</div>
            <div class="text-xs text-green-400 mt-1">↑ 12.5%</div>
        </div>
        <div class="screen-card rounded-lg p-4 text-center">
            <div class="text-3xl data-number text-green-400 mb-2">1,247</div>
            <div class="text-sm text-gray-300">数据表数量</div>
            <div class="text-xs text-green-400 mt-1">↑ 8.3%</div>
        </div>
        <div class="screen-card rounded-lg p-4 text-center">
            <div class="text-3xl data-number text-purple-400 mb-2">89</div>
            <div class="text-sm text-gray-300">ETL任务</div>
            <div class="text-xs text-blue-400 mt-1">运行中</div>
        </div>
        <div class="screen-card rounded-lg p-4 text-center">
            <div class="text-3xl data-number text-orange-400 mb-2">156</div>
            <div class="text-sm text-gray-300">质量规则</div>
            <div class="text-xs text-green-400 mt-1">89.5% 通过</div>
        </div>
        <div class="screen-card rounded-lg p-4 text-center">
            <div class="text-3xl data-number text-red-400 mb-2">23</div>
            <div class="text-sm text-gray-300">异常告警</div>
            <div class="text-xs text-red-400 mt-1">需处理</div>
        </div>
        <div class="screen-card rounded-lg p-4 text-center">
            <div class="text-3xl data-number text-cyan-400 mb-2">2,847</div>
            <div class="text-sm text-gray-300">API调用</div>
            <div class="text-xs text-green-400 mt-1">今日</div>
        </div>
    </div>

    <!-- 主要图表区域 -->
    <div class="grid grid-cols-3 gap-4 mb-6">
        <!-- 数据处理趋势 -->
        <div class="screen-card rounded-lg p-4">
            <h3 class="text-lg font-semibold mb-3 glow-text">数据处理趋势</h3>
            <div class="large-chart">
                <canvas id="processingTrendChart"></canvas>
            </div>
        </div>

        <!-- 系统资源使用 -->
        <div class="screen-card rounded-lg p-4">
            <h3 class="text-lg font-semibold mb-3 glow-text">系统资源使用</h3>
            <div class="large-chart">
                <canvas id="resourceUsageChart"></canvas>
            </div>
        </div>

        <!-- 数据质量分布 -->
        <div class="screen-card rounded-lg p-4">
            <h3 class="text-lg font-semibold mb-3 glow-text">数据质量分布</h3>
            <div class="large-chart">
                <canvas id="qualityDistributionChart"></canvas>
            </div>
        </div>
    </div>

    <!-- 底部详细信息 -->
    <div class="grid grid-cols-4 gap-4">
        <!-- 实时任务状态 -->
        <div class="screen-card rounded-lg p-4">
            <h3 class="text-lg font-semibold mb-3 glow-text">实时任务状态</h3>
            <div class="space-y-2" id="taskStatus">
                <!-- 任务状态将动态加载 -->
            </div>
        </div>

        <!-- 数据源连接状态 -->
        <div class="screen-card rounded-lg p-4">
            <h3 class="text-lg font-semibold mb-3 glow-text">数据源状态</h3>
            <div class="space-y-2" id="dataSourceStatus">
                <!-- 数据源状态将动态加载 -->
            </div>
        </div>

        <!-- 存储节点状态 -->
        <div class="screen-card rounded-lg p-4">
            <h3 class="text-lg font-semibold mb-3 glow-text">存储节点</h3>
            <div class="space-y-2" id="storageNodes">
                <!-- 存储节点状态将动态加载 -->
            </div>
        </div>

        <!-- 最新告警信息 -->
        <div class="screen-card rounded-lg p-4">
            <h3 class="text-lg font-semibold mb-3 glow-text">最新告警</h3>
            <div class="space-y-2" id="latestAlerts">
                <!-- 告警信息将动态加载 -->
            </div>
        </div>
    </div>

    <script>
        let charts = {};

        document.addEventListener('DOMContentLoaded', function() {
            initDashboard();
            updateTime();
            setInterval(updateTime, 1000);
            setInterval(updateRealTimeData, 5000);
        });

        function initDashboard() {
            createCharts();
            loadRealTimeData();
        }

        function updateTime() {
            const now = new Date();
            document.getElementById('currentTime').textContent = now.toLocaleString('zh-CN');
        }

        function createCharts() {
            // 数据处理趋势图
            const processingCtx = document.getElementById('processingTrendChart').getContext('2d');
            charts.processing = new Chart(processingCtx, {
                type: 'line',
                data: {
                    labels: ['00:00', '04:00', '08:00', '12:00', '16:00', '20:00'],
                    datasets: [{
                        label: '数据处理量(GB)',
                        data: [120, 189, 245, 389, 456, 298],
                        borderColor: '#3b82f6',
                        backgroundColor: 'rgba(59, 130, 246, 0.1)',
                        tension: 0.4,
                        fill: true
                    }, {
                        label: '任务完成数',
                        data: [45, 67, 89, 123, 156, 134],
                        borderColor: '#10b981',
                        backgroundColor: 'rgba(16, 185, 129, 0.1)',
                        tension: 0.4
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: { 
                            labels: { color: 'white' }
                        }
                    },
                    scales: {
                        x: { 
                            ticks: { color: 'white' },
                            grid: { color: 'rgba(255, 255, 255, 0.1)' }
                        },
                        y: { 
                            ticks: { color: 'white' },
                            grid: { color: 'rgba(255, 255, 255, 0.1)' }
                        }
                    }
                }
            });

            // 系统资源使用图
            const resourceCtx = document.getElementById('resourceUsageChart').getContext('2d');
            charts.resource = new Chart(resourceCtx, {
                type: 'doughnut',
                data: {
                    labels: ['CPU使用率', 'CPU空闲', '内存使用率', '内存空闲', '磁盘使用率', '磁盘空闲'],
                    datasets: [{
                        data: [65, 35, 78, 22, 45, 55],
                        backgroundColor: [
                            '#ef4444', '#10b981',
                            '#f59e0b', '#10b981',
                            '#8b5cf6', '#10b981'
                        ]
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: { 
                            labels: { color: 'white', font: { size: 10 } }
                        }
                    }
                }
            });

            // 数据质量分布图
            const qualityCtx = document.getElementById('qualityDistributionChart').getContext('2d');
            charts.quality = new Chart(qualityCtx, {
                type: 'radar',
                data: {
                    labels: ['完整性', '准确性', '一致性', '时效性', '有效性', '唯一性'],
                    datasets: [{
                        label: '当前质量评分',
                        data: [92, 87, 89, 85, 91, 88],
                        borderColor: '#3b82f6',
                        backgroundColor: 'rgba(59, 130, 246, 0.2)',
                        pointBackgroundColor: '#3b82f6'
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: { 
                            labels: { color: 'white' }
                        }
                    },
                    scales: {
                        r: {
                            beginAtZero: true,
                            max: 100,
                            ticks: { color: 'white' },
                            grid: { color: 'rgba(255, 255, 255, 0.1)' },
                            pointLabels: { color: 'white' }
                        }
                    }
                }
            });
        }

        function loadRealTimeData() {
            // 加载实时任务状态
            const taskStatus = document.getElementById('taskStatus');
            const tasks = [
                { name: '矿产数据ETL', status: 'running', progress: 78 },
                { name: '质量检查任务', status: 'completed', progress: 100 },
                { name: '数据同步任务', status: 'running', progress: 45 },
                { name: '报告生成任务', status: 'pending', progress: 0 },
                { name: '备份任务', status: 'running', progress: 92 }
            ];

            taskStatus.innerHTML = tasks.map(task => `
                <div class="flex items-center justify-between text-sm">
                    <div class="flex items-center">
                        <span class="status-indicator ${getTaskStatusClass(task.status)}"></span>
                        <span class="truncate">${task.name}</span>
                    </div>
                    <span class="data-number ${getTaskProgressColor(task.progress)}">${task.progress}%</span>
                </div>
            `).join('');

            // 加载数据源状态
            const dataSourceStatus = document.getElementById('dataSourceStatus');
            const dataSources = [
                { name: 'MySQL主库', status: 'online', latency: '2ms' },
                { name: 'Oracle数据库', status: 'online', latency: '5ms' },
                { name: 'Hive数据仓库', status: 'online', latency: '12ms' },
                { name: 'MongoDB集群', status: 'warning', latency: '45ms' },
                { name: 'Redis缓存', status: 'online', latency: '1ms' }
            ];

            dataSourceStatus.innerHTML = dataSources.map(ds => `
                <div class="flex items-center justify-between text-sm">
                    <div class="flex items-center">
                        <span class="status-indicator status-${ds.status}"></span>
                        <span class="truncate">${ds.name}</span>
                    </div>
                    <span class="data-number text-gray-300">${ds.latency}</span>
                </div>
            `).join('');

            // 加载存储节点状态
            const storageNodes = document.getElementById('storageNodes');
            const nodes = [
                { name: 'HDFS-Node1', usage: 65, capacity: '2TB' },
                { name: 'HDFS-Node2', usage: 78, capacity: '2TB' },
                { name: 'HDFS-Node3', usage: 45, capacity: '2TB' },
                { name: 'MinIO-Cluster', usage: 89, capacity: '5TB' },
                { name: 'Local-Storage', usage: 34, capacity: '1TB' }
            ];

            storageNodes.innerHTML = nodes.map(node => `
                <div class="text-sm">
                    <div class="flex items-center justify-between mb-1">
                        <span class="truncate">${node.name}</span>
                        <span class="data-number text-gray-300">${node.capacity}</span>
                    </div>
                    <div class="w-full bg-gray-700 rounded-full h-1">
                        <div class="bg-blue-500 h-1 rounded-full" style="width: ${node.usage}%"></div>
                    </div>
                    <div class="text-xs text-gray-400 mt-1">${node.usage}% 已使用</div>
                </div>
            `).join('');

            // 加载最新告警
            const latestAlerts = document.getElementById('latestAlerts');
            const alerts = [
                { level: 'error', message: '数据质量检查失败', time: '2分钟前' },
                { level: 'warning', message: 'ETL任务执行缓慢', time: '5分钟前' },
                { level: 'warning', message: '存储空间不足', time: '8分钟前' },
                { level: 'info', message: '系统备份完成', time: '15分钟前' },
                { level: 'error', message: '数据源连接异常', time: '20分钟前' }
            ];

            latestAlerts.innerHTML = alerts.map(alert => `
                <div class="text-sm">
                    <div class="flex items-center">
                        <i class="fas fa-circle text-${getAlertColor(alert.level)} text-xs mr-2"></i>
                        <span class="flex-1 truncate">${alert.message}</span>
                    </div>
                    <div class="text-xs text-gray-400 ml-4">${alert.time}</div>
                </div>
            `).join('');
        }

        function updateRealTimeData() {
            // 更新图表数据
            if (charts.processing) {
                const newData = Math.floor(Math.random() * 100 + 200);
                charts.processing.data.datasets[0].data.push(newData);
                charts.processing.data.datasets[0].data.shift();
                charts.processing.update('none');
            }

            // 更新其他实时数据
            loadRealTimeData();
        }

        function getTaskStatusClass(status) {
            const classes = {
                running: 'status-online pulse-dot',
                completed: 'status-online',
                pending: 'status-warning',
                failed: 'status-offline'
            };
            return classes[status] || 'status-warning';
        }

        function getTaskProgressColor(progress) {
            if (progress >= 90) return 'text-green-400';
            if (progress >= 50) return 'text-blue-400';
            if (progress > 0) return 'text-yellow-400';
            return 'text-gray-400';
        }

        function getAlertColor(level) {
            const colors = {
                error: 'red-400',
                warning: 'yellow-400',
                info: 'blue-400'
            };
            return colors[level] || 'gray-400';
        }

        // 全屏切换功能
        document.addEventListener('keydown', function(e) {
            if (e.key === 'F11') {
                e.preventDefault();
                if (!document.fullscreenElement) {
                    document.documentElement.requestFullscreen();
                } else {
                    document.exitFullscreen();
                }
            }
        });

        // 自动刷新页面数据
        setInterval(() => {
            // 模拟数据更新
            const elements = document.querySelectorAll('.data-number');
            elements.forEach(el => {
                if (el.textContent.includes('TB') || el.textContent.includes('GB')) return;
                if (el.textContent.includes('%')) return;
                if (el.textContent.includes(':')) return;
                
                const currentValue = parseInt(el.textContent.replace(/,/g, ''));
                if (!isNaN(currentValue)) {
                    const variation = Math.floor(Math.random() * 20 - 10); // -10 到 +10 的变化
                    const newValue = Math.max(0, currentValue + variation);
                    el.textContent = newValue.toLocaleString();
                }
            });
        }, 10000);
    </script>
</body>
</html>
