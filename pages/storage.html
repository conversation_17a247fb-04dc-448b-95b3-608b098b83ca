<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>统一存储管理</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <link rel="stylesheet" href="../css/style.css">
</head>
<body class="bg-gray-100 p-6">
    <!-- 页面头部 -->
    <div class="mb-6">
        <div class="flex items-center justify-between">
            <div>
                <h1 class="text-2xl font-bold text-gray-900">统一存储管理</h1>
                <p class="text-gray-600 mt-1">管理和监控分布式存储系统</p>
            </div>
            <div class="flex items-center space-x-4">
                <button onclick="showAddStorageModal()" class="btn btn-primary">
                    <i class="fas fa-plus mr-2"></i>添加存储节点
                </button>
                <button onclick="refreshStorage()" class="btn btn-outline">
                    <i class="fas fa-sync-alt mr-2"></i>刷新
                </button>
            </div>
        </div>
    </div>

    <!-- 存储概览统计 -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <div class="card">
            <div class="card-body">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-600">总存储容量</p>
                        <p class="text-2xl font-bold text-gray-900">10.5 PB</p>
                    </div>
                    <div class="p-3 bg-blue-100 rounded-full">
                        <i class="fas fa-hdd text-blue-600 text-xl"></i>
                    </div>
                </div>
            </div>
        </div>
        <div class="card">
            <div class="card-body">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-600">已使用容量</p>
                        <p class="text-2xl font-bold text-gray-900">2.5 TB</p>
                    </div>
                    <div class="p-3 bg-green-100 rounded-full">
                        <i class="fas fa-database text-green-600 text-xl"></i>
                    </div>
                </div>
            </div>
        </div>
        <div class="card">
            <div class="card-body">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-600">使用率</p>
                        <p class="text-2xl font-bold text-gray-900">23.8%</p>
                    </div>
                    <div class="p-3 bg-yellow-100 rounded-full">
                        <i class="fas fa-chart-pie text-yellow-600 text-xl"></i>
                    </div>
                </div>
            </div>
        </div>
        <div class="card">
            <div class="card-body">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-600">活跃节点</p>
                        <p class="text-2xl font-bold text-gray-900">24/26</p>
                    </div>
                    <div class="p-3 bg-purple-100 rounded-full">
                        <i class="fas fa-server text-purple-600 text-xl"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 存储使用趋势图表 -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
        <div class="card">
            <div class="card-body">
                <h3 class="text-lg font-semibold mb-4">存储使用趋势</h3>
                <div class="chart-container">
                    <canvas id="storageUsageChart"></canvas>
                </div>
            </div>
        </div>
        <div class="card">
            <div class="card-body">
                <h3 class="text-lg font-semibold mb-4">存储类型分布</h3>
                <div class="chart-container">
                    <canvas id="storageTypeChart"></canvas>
                </div>
            </div>
        </div>
    </div>

    <!-- 存储节点列表 -->
    <div class="card">
        <div class="card-body">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg font-semibold">存储节点列表</h3>
                <div class="flex items-center space-x-4">
                    <div class="relative">
                        <input type="text" id="searchInput" placeholder="搜索节点..." 
                               class="form-input pl-10 pr-4 py-2 w-64">
                        <i class="fas fa-search absolute left-3 top-3 text-gray-400"></i>
                    </div>
                    <select id="statusFilter" class="form-select">
                        <option value="">全部状态</option>
                        <option value="online">在线</option>
                        <option value="offline">离线</option>
                        <option value="maintenance">维护中</option>
                    </select>
                </div>
            </div>
            
            <div class="overflow-x-auto">
                <table class="table" id="storageTable">
                    <thead>
                        <tr>
                            <th>节点名称</th>
                            <th>IP地址</th>
                            <th>存储类型</th>
                            <th>总容量</th>
                            <th>已使用</th>
                            <th>使用率</th>
                            <th>状态</th>
                            <th>最后更新</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody id="storageTableBody">
                        <!-- 数据将通过JavaScript动态加载 -->
                    </tbody>
                </table>
            </div>
            
            <!-- 分页 -->
            <div class="flex items-center justify-between mt-4">
                <div class="text-sm text-gray-700">
                    显示 <span id="pageInfo">1-10</span> 条，共 <span id="totalCount">26</span> 条记录
                </div>
                <div class="flex items-center space-x-2">
                    <button onclick="changePage(-1)" class="btn btn-outline btn-sm">
                        <i class="fas fa-chevron-left"></i>
                    </button>
                    <span id="currentPage" class="px-3 py-1 bg-blue-100 text-blue-800 rounded">1</span>
                    <button onclick="changePage(1)" class="btn btn-outline btn-sm">
                        <i class="fas fa-chevron-right"></i>
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script>
        let currentPage = 1;
        const pageSize = 10;
        let storageData = [];

        document.addEventListener('DOMContentLoaded', function() {
            initStorage();
        });

        function initStorage() {
            generateMockData();
            createStorageCharts();
            renderStorageTable();
            
            // 搜索功能
            document.getElementById('searchInput').addEventListener('input', function() {
                renderStorageTable();
            });
            
            // 状态筛选
            document.getElementById('statusFilter').addEventListener('change', function() {
                renderStorageTable();
            });
        }

        function generateMockData() {
            const nodeTypes = ['SSD', 'HDD', 'NVMe', 'Hybrid'];
            const statuses = ['online', 'offline', 'maintenance'];
            const statusLabels = { online: '在线', offline: '离线', maintenance: '维护中' };
            
            for (let i = 1; i <= 26; i++) {
                const totalCapacity = Math.floor(Math.random() * 500 + 100); // 100-600GB
                const usedCapacity = Math.floor(totalCapacity * (Math.random() * 0.8 + 0.1)); // 10-90%使用率
                const status = statuses[Math.floor(Math.random() * statuses.length)];
                
                storageData.push({
                    id: i,
                    name: `storage-node-${i.toString().padStart(2, '0')}`,
                    ip: `192.168.1.${100 + i}`,
                    type: nodeTypes[Math.floor(Math.random() * nodeTypes.length)],
                    totalCapacity: totalCapacity,
                    usedCapacity: usedCapacity,
                    usageRate: Math.round((usedCapacity / totalCapacity) * 100),
                    status: status,
                    statusLabel: statusLabels[status],
                    lastUpdate: new Date(Date.now() - Math.random() * 3600000).toLocaleString('zh-CN')
                });
            }
        }

        function createStorageCharts() {
            // 存储使用趋势图
            const usageCtx = document.getElementById('storageUsageChart').getContext('2d');
            new Chart(usageCtx, {
                type: 'line',
                data: {
                    labels: ['1月', '2月', '3月', '4月', '5月', '6月'],
                    datasets: [{
                        label: '使用量(TB)',
                        data: [1.2, 1.5, 1.8, 2.0, 2.3, 2.5],
                        borderColor: 'rgb(59, 130, 246)',
                        backgroundColor: 'rgba(59, 130, 246, 0.1)',
                        tension: 0.4,
                        fill: true
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: { legend: { display: false } }
                }
            });

            // 存储类型分布图
            const typeCtx = document.getElementById('storageTypeChart').getContext('2d');
            new Chart(typeCtx, {
                type: 'doughnut',
                data: {
                    labels: ['SSD', 'HDD', 'NVMe', 'Hybrid'],
                    datasets: [{
                        data: [35, 40, 15, 10],
                        backgroundColor: ['#3b82f6', '#10b981', '#f59e0b', '#8b5cf6']
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: { legend: { position: 'bottom' } }
                }
            });
        }

        function renderStorageTable() {
            const searchTerm = document.getElementById('searchInput').value.toLowerCase();
            const statusFilter = document.getElementById('statusFilter').value;
            
            let filteredData = storageData.filter(item => {
                const matchesSearch = item.name.toLowerCase().includes(searchTerm) || 
                                    item.ip.includes(searchTerm);
                const matchesStatus = !statusFilter || item.status === statusFilter;
                return matchesSearch && matchesStatus;
            });
            
            const startIndex = (currentPage - 1) * pageSize;
            const endIndex = startIndex + pageSize;
            const pageData = filteredData.slice(startIndex, endIndex);
            
            const tbody = document.getElementById('storageTableBody');
            tbody.innerHTML = pageData.map(item => `
                <tr>
                    <td class="font-medium">${item.name}</td>
                    <td>${item.ip}</td>
                    <td><span class="px-2 py-1 bg-gray-100 rounded text-sm">${item.type}</span></td>
                    <td>${item.totalCapacity}GB</td>
                    <td>${item.usedCapacity}GB</td>
                    <td>
                        <div class="flex items-center">
                            <div class="w-16 bg-gray-200 rounded-full h-2 mr-2">
                                <div class="bg-blue-600 h-2 rounded-full" style="width: ${item.usageRate}%"></div>
                            </div>
                            <span class="text-sm">${item.usageRate}%</span>
                        </div>
                    </td>
                    <td>
                        <span class="status-badge ${getStatusClass(item.status)}">${item.statusLabel}</span>
                    </td>
                    <td class="text-sm text-gray-500">${item.lastUpdate}</td>
                    <td>
                        <div class="flex items-center space-x-2">
                            <button onclick="viewStorageDetails(${item.id})" class="text-blue-600 hover:text-blue-800">
                                <i class="fas fa-eye"></i>
                            </button>
                            <button onclick="editStorage(${item.id})" class="text-green-600 hover:text-green-800">
                                <i class="fas fa-edit"></i>
                            </button>
                            <button onclick="deleteStorage(${item.id})" class="text-red-600 hover:text-red-800">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                    </td>
                </tr>
            `).join('');
            
            // 更新分页信息
            document.getElementById('pageInfo').textContent = `${startIndex + 1}-${Math.min(endIndex, filteredData.length)}`;
            document.getElementById('totalCount').textContent = filteredData.length;
            document.getElementById('currentPage').textContent = currentPage;
        }

        function getStatusClass(status) {
            const classes = {
                online: 'status-success',
                offline: 'status-error',
                maintenance: 'status-warning'
            };
            return classes[status] || 'status-info';
        }

        function changePage(direction) {
            const newPage = currentPage + direction;
            if (newPage >= 1 && newPage <= Math.ceil(storageData.length / pageSize)) {
                currentPage = newPage;
                renderStorageTable();
            }
        }

        function refreshStorage() {
            if (parent.showToast) {
                parent.showToast('存储数据刷新成功', 'success');
            }
            renderStorageTable();
        }

        function showAddStorageModal() {
            if (parent.showModal) {
                parent.showModal('添加存储节点', `
                    <form id="addStorageForm" class="space-y-4">
                        <div class="form-group">
                            <label class="form-label">节点名称</label>
                            <input type="text" class="form-input" required>
                        </div>
                        <div class="form-group">
                            <label class="form-label">IP地址</label>
                            <input type="text" class="form-input" required>
                        </div>
                        <div class="form-group">
                            <label class="form-label">存储类型</label>
                            <select class="form-select" required>
                                <option value="">请选择</option>
                                <option value="SSD">SSD</option>
                                <option value="HDD">HDD</option>
                                <option value="NVMe">NVMe</option>
                                <option value="Hybrid">Hybrid</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label class="form-label">存储容量(GB)</label>
                            <input type="number" class="form-input" required>
                        </div>
                    </form>
                `, [
                    { text: '取消', class: 'btn-outline' },
                    { text: '添加', class: 'btn-primary', onclick: 'addStorage()' }
                ]);
            }
        }

        function addStorage() {
            if (parent.showToast) {
                parent.showToast('存储节点添加成功', 'success');
            }
        }

        function viewStorageDetails(id) {
            const storage = storageData.find(item => item.id === id);
            if (parent.showModal && storage) {
                parent.showModal('存储节点详情', `
                    <div class="space-y-3">
                        <div><strong>节点名称：</strong>${storage.name}</div>
                        <div><strong>IP地址：</strong>${storage.ip}</div>
                        <div><strong>存储类型：</strong>${storage.type}</div>
                        <div><strong>总容量：</strong>${storage.totalCapacity}GB</div>
                        <div><strong>已使用：</strong>${storage.usedCapacity}GB</div>
                        <div><strong>使用率：</strong>${storage.usageRate}%</div>
                        <div><strong>状态：</strong>${storage.statusLabel}</div>
                        <div><strong>最后更新：</strong>${storage.lastUpdate}</div>
                    </div>
                `);
            }
        }

        function editStorage(id) {
            if (parent.showToast) {
                parent.showToast('编辑功能开发中', 'info');
            }
        }

        function deleteStorage(id) {
            if (parent.showConfirm) {
                parent.showConfirm('确定要删除这个存储节点吗？', function() {
                    if (parent.showToast) {
                        parent.showToast('存储节点删除成功', 'success');
                    }
                });
            }
        }
    </script>
</body>
</html>
