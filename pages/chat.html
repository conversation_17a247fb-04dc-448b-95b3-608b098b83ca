<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>智能对话</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="../css/style.css">
</head>
<body class="bg-gray-100 h-screen flex flex-col">
    <!-- 页面头部 -->
    <div class="bg-white shadow-sm border-b p-4">
        <div class="flex items-center justify-between">
            <div>
                <h1 class="text-2xl font-bold text-gray-900">智能对话分析</h1>
                <p class="text-gray-600 mt-1">基于AI的交互式数据查询与分析</p>
            </div>
            <div class="flex items-center space-x-4">
                <button onclick="clearChat()" class="btn btn-outline">
                    <i class="fas fa-trash mr-2"></i>清空对话
                </button>
                <button onclick="exportChat()" class="btn btn-primary">
                    <i class="fas fa-download mr-2"></i>导出对话
                </button>
            </div>
        </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="flex-1 flex overflow-hidden">
        <!-- 左侧边栏 - 对话历史和快捷操作 -->
        <div class="w-80 bg-white border-r flex flex-col">
            <!-- 快捷问题 -->
            <div class="p-4 border-b">
                <h3 class="font-semibold mb-3">快捷问题</h3>
                <div class="space-y-2">
                    <button onclick="sendQuickQuestion('显示最近一周的矿产产量数据')" 
                            class="w-full text-left p-3 bg-blue-50 hover:bg-blue-100 rounded-lg text-sm transition-colors">
                        <i class="fas fa-chart-bar mr-2 text-blue-600"></i>
                        显示最近一周的矿产产量数据
                    </button>
                    <button onclick="sendQuickQuestion('分析各地区矿产资源分布情况')" 
                            class="w-full text-left p-3 bg-green-50 hover:bg-green-100 rounded-lg text-sm transition-colors">
                        <i class="fas fa-map-marked-alt mr-2 text-green-600"></i>
                        分析各地区矿产资源分布情况
                    </button>
                    <button onclick="sendQuickQuestion('查看数据质量评估报告')" 
                            class="w-full text-left p-3 bg-purple-50 hover:bg-purple-100 rounded-lg text-sm transition-colors">
                        <i class="fas fa-check-circle mr-2 text-purple-600"></i>
                        查看数据质量评估报告
                    </button>
                    <button onclick="sendQuickQuestion('生成月度生产统计报表')" 
                            class="w-full text-left p-3 bg-orange-50 hover:bg-orange-100 rounded-lg text-sm transition-colors">
                        <i class="fas fa-file-alt mr-2 text-orange-600"></i>
                        生成月度生产统计报表
                    </button>
                </div>
            </div>

            <!-- 对话历史 -->
            <div class="flex-1 p-4">
                <h3 class="font-semibold mb-3">对话历史</h3>
                <div class="space-y-2" id="chatHistory">
                    <div class="p-3 bg-gray-50 rounded-lg cursor-pointer hover:bg-gray-100">
                        <div class="font-medium text-sm">矿产产量分析</div>
                        <div class="text-xs text-gray-500 mt-1">2024-01-15 14:30</div>
                    </div>
                    <div class="p-3 bg-gray-50 rounded-lg cursor-pointer hover:bg-gray-100">
                        <div class="font-medium text-sm">资源分布查询</div>
                        <div class="text-xs text-gray-500 mt-1">2024-01-15 10:15</div>
                    </div>
                    <div class="p-3 bg-gray-50 rounded-lg cursor-pointer hover:bg-gray-100">
                        <div class="font-medium text-sm">质量评估报告</div>
                        <div class="text-xs text-gray-500 mt-1">2024-01-14 16:45</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 主对话区域 -->
        <div class="flex-1 flex flex-col">
            <!-- 对话内容 -->
            <div class="flex-1 overflow-y-auto p-6" id="chatMessages">
                <!-- 欢迎消息 -->
                <div class="flex items-start space-x-3 mb-6">
                    <div class="w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center">
                        <i class="fas fa-robot text-white text-sm"></i>
                    </div>
                    <div class="flex-1">
                        <div class="bg-blue-50 rounded-lg p-4">
                            <p class="text-gray-800">您好！我是智能数据分析助手。我可以帮您：</p>
                            <ul class="mt-2 space-y-1 text-sm text-gray-600">
                                <li>• 查询和分析矿产资源数据</li>
                                <li>• 生成各类统计报表</li>
                                <li>• 进行数据质量评估</li>
                                <li>• 提供数据洞察和建议</li>
                            </ul>
                            <p class="mt-2 text-gray-800">请告诉我您想了解什么？</p>
                        </div>
                        <div class="text-xs text-gray-500 mt-1">刚刚</div>
                    </div>
                </div>
            </div>

            <!-- 输入区域 -->
            <div class="border-t bg-white p-4">
                <div class="flex items-end space-x-4">
                    <div class="flex-1">
                        <textarea 
                            id="messageInput" 
                            placeholder="请输入您的问题..." 
                            class="w-full p-3 border border-gray-300 rounded-lg resize-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                            rows="3"
                            onkeydown="handleKeyDown(event)"
                        ></textarea>
                    </div>
                    <div class="flex flex-col space-y-2">
                        <button onclick="sendMessage()" class="btn btn-primary px-6">
                            <i class="fas fa-paper-plane mr-2"></i>发送
                        </button>
                        <button onclick="voiceInput()" class="btn btn-outline px-6">
                            <i class="fas fa-microphone mr-2"></i>语音
                        </button>
                    </div>
                </div>
                
                <!-- 输入提示 -->
                <div class="flex items-center justify-between mt-2 text-xs text-gray-500">
                    <div>按 Ctrl+Enter 发送消息</div>
                    <div id="inputCounter">0/1000</div>
                </div>
            </div>
        </div>
    </div>

    <script>
        let messageCount = 0;

        document.addEventListener('DOMContentLoaded', function() {
            initChat();
        });

        function initChat() {
            // 输入框字符计数
            const messageInput = document.getElementById('messageInput');
            messageInput.addEventListener('input', function() {
                const counter = document.getElementById('inputCounter');
                counter.textContent = `${this.value.length}/1000`;
                
                if (this.value.length > 1000) {
                    counter.classList.add('text-red-500');
                } else {
                    counter.classList.remove('text-red-500');
                }
            });
        }

        function handleKeyDown(event) {
            if (event.ctrlKey && event.key === 'Enter') {
                event.preventDefault();
                sendMessage();
            }
        }

        function sendMessage() {
            const input = document.getElementById('messageInput');
            const message = input.value.trim();
            
            if (!message) return;
            
            // 添加用户消息
            addMessage(message, 'user');
            
            // 清空输入框
            input.value = '';
            document.getElementById('inputCounter').textContent = '0/1000';
            
            // 模拟AI回复
            setTimeout(() => {
                const response = generateAIResponse(message);
                addMessage(response, 'ai');
            }, 1000 + Math.random() * 2000);
        }

        function sendQuickQuestion(question) {
            const input = document.getElementById('messageInput');
            input.value = question;
            sendMessage();
        }

        function addMessage(content, sender) {
            const messagesContainer = document.getElementById('chatMessages');
            const messageDiv = document.createElement('div');
            messageDiv.className = 'flex items-start space-x-3 mb-6';
            
            const isUser = sender === 'user';
            const time = new Date().toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' });
            
            messageDiv.innerHTML = `
                <div class="w-8 h-8 ${isUser ? 'bg-green-600' : 'bg-blue-600'} rounded-full flex items-center justify-center">
                    <i class="fas ${isUser ? 'fa-user' : 'fa-robot'} text-white text-sm"></i>
                </div>
                <div class="flex-1">
                    <div class="${isUser ? 'bg-green-50' : 'bg-blue-50'} rounded-lg p-4">
                        ${formatMessage(content)}
                    </div>
                    <div class="text-xs text-gray-500 mt-1">${time}</div>
                </div>
            `;
            
            messagesContainer.appendChild(messageDiv);
            messagesContainer.scrollTop = messagesContainer.scrollHeight;
            messageCount++;
        }

        function formatMessage(content) {
            // 简单的消息格式化，支持基本的HTML
            return content
                .replace(/\n/g, '<br>')
                .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
                .replace(/\*(.*?)\*/g, '<em>$1</em>');
        }

        function generateAIResponse(userMessage) {
            const responses = {
                '产量': `根据最新数据分析，最近一周的矿产产量情况如下：

**总产量：** 125,000吨
**日均产量：** 17,857吨
**环比增长：** +5.2%

**分类产量：**
• 铁矿石：85,000吨 (68%)
• 铜矿：25,000吨 (20%)
• 其他矿产：15,000吨 (12%)

**趋势分析：**
产量呈稳步上升趋势，主要得益于新设备投入使用和工艺优化。建议继续保持当前生产节奏。`,

                '分布': `各地区矿产资源分布分析报告：

**华北地区：**
• 主要矿种：煤炭、铁矿石
• 储量占比：35%
• 开采状况：良好

**西北地区：**
• 主要矿种：有色金属、稀土
• 储量占比：28%
• 开采状况：稳定

**西南地区：**
• 主要矿种：磷矿、铝土矿
• 储量占比：22%
• 开采状况：优秀

**其他地区：**
• 储量占比：15%
• 开采状况：一般`,

                '质量': `数据质量评估报告：

**整体评分：** 89.5分 (优秀)

**详细评估：**
• **完整性：** 92% - 数据缺失率较低
• **准确性：** 88% - 少量数据需要校正
• **一致性：** 91% - 格式基本统一
• **时效性：** 87% - 部分数据更新滞后

**问题识别：**
1. 部分历史数据存在格式不一致
2. 实时数据同步偶有延迟
3. 少量重复记录需要清理

**改进建议：**
1. 建立数据标准化流程
2. 优化实时同步机制
3. 定期进行数据清洗`,

                '报表': `月度生产统计报表已生成：

**报表概要：**
• 报表周期：2024年1月
• 数据来源：生产管理系统
• 生成时间：${new Date().toLocaleString('zh-CN')}

**主要指标：**
• 月度总产量：520,000吨
• 月度目标完成率：104.2%
• 设备利用率：87.5%
• 安全事故：0起

**下载链接：**
[月度生产统计报表.xlsx] - 点击下载完整报表

报表已自动发送至相关负责人邮箱。`
            };

            // 根据关键词匹配回复
            for (const [keyword, response] of Object.entries(responses)) {
                if (userMessage.includes(keyword)) {
                    return response;
                }
            }

            // 默认回复
            return `我理解您的问题："${userMessage}"

我正在分析相关数据，请稍等片刻...

**可能的分析方向：**
• 数据查询和统计分析
• 趋势预测和对比分析  
• 异常检测和质量评估
• 报表生成和数据导出

如果您需要更具体的分析，请提供更多详细信息，比如：
- 具体的时间范围
- 关注的指标类型
- 需要的分析维度

我会为您提供更精准的分析结果。`;
        }

        function voiceInput() {
            if (parent.showToast) {
                parent.showToast('语音输入功能开发中', 'info');
            }
        }

        function clearChat() {
            if (parent.showConfirm) {
                parent.showConfirm('确定要清空所有对话记录吗？', function() {
                    const messagesContainer = document.getElementById('chatMessages');
                    // 保留欢迎消息
                    const welcomeMessage = messagesContainer.firstElementChild;
                    messagesContainer.innerHTML = '';
                    messagesContainer.appendChild(welcomeMessage);
                    messageCount = 0;
                    
                    if (parent.showToast) {
                        parent.showToast('对话记录已清空', 'success');
                    }
                });
            }
        }

        function exportChat() {
            if (parent.showToast) {
                parent.showToast('对话导出功能开发中', 'info');
            }
        }
    </script>
</body>
</html>
