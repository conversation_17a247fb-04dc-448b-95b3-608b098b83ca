<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>可视化报告</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <link rel="stylesheet" href="../css/style.css">
</head>
<body class="bg-gray-100 p-6">
    <!-- 页面头部 -->
    <div class="mb-6">
        <div class="flex items-center justify-between">
            <div>
                <h1 class="text-2xl font-bold text-gray-900">可视化报告</h1>
                <p class="text-gray-600 mt-1">创建和管理数据可视化报告</p>
            </div>
            <div class="flex items-center space-x-4">
                <button onclick="showCreateReportModal()" class="btn btn-primary">
                    <i class="fas fa-plus mr-2"></i>创建报告
                </button>
                <button onclick="showTemplateModal()" class="btn btn-outline">
                    <i class="fas fa-layer-group mr-2"></i>报告模板
                </button>
            </div>
        </div>
    </div>

    <!-- 报告统计 -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <div class="card">
            <div class="card-body">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-600">总报告数</p>
                        <p class="text-2xl font-bold text-gray-900">89</p>
                    </div>
                    <div class="p-3 bg-blue-100 rounded-full">
                        <i class="fas fa-chart-bar text-blue-600 text-xl"></i>
                    </div>
                </div>
            </div>
        </div>
        <div class="card">
            <div class="card-body">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-600">本月新增</p>
                        <p class="text-2xl font-bold text-green-600">12</p>
                    </div>
                    <div class="p-3 bg-green-100 rounded-full">
                        <i class="fas fa-plus-circle text-green-600 text-xl"></i>
                    </div>
                </div>
            </div>
        </div>
        <div class="card">
            <div class="card-body">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-600">活跃报告</p>
                        <p class="text-2xl font-bold text-purple-600">67</p>
                    </div>
                    <div class="p-3 bg-purple-100 rounded-full">
                        <i class="fas fa-eye text-purple-600 text-xl"></i>
                    </div>
                </div>
            </div>
        </div>
        <div class="card">
            <div class="card-body">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-600">总访问量</p>
                        <p class="text-2xl font-bold text-orange-600">2.3K</p>
                    </div>
                    <div class="p-3 bg-orange-100 rounded-full">
                        <i class="fas fa-mouse-pointer text-orange-600 text-xl"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 筛选和搜索 -->
    <div class="card mb-6">
        <div class="card-body">
            <div class="flex items-center justify-between">
                <div class="flex items-center space-x-4">
                    <div class="relative">
                        <input type="text" id="searchInput" placeholder="搜索报告..." 
                               class="form-input pl-10 pr-4 py-2 w-64">
                        <i class="fas fa-search absolute left-3 top-3 text-gray-400"></i>
                    </div>
                    <select id="categoryFilter" class="form-select">
                        <option value="">全部分类</option>
                        <option value="production">生产报告</option>
                        <option value="quality">质量报告</option>
                        <option value="financial">财务报告</option>
                        <option value="safety">安全报告</option>
                    </select>
                    <select id="statusFilter" class="form-select">
                        <option value="">全部状态</option>
                        <option value="published">已发布</option>
                        <option value="draft">草稿</option>
                        <option value="archived">已归档</option>
                    </select>
                </div>
                <div class="flex items-center space-x-2">
                    <button onclick="toggleView('grid')" id="gridViewBtn" class="p-2 text-blue-600 bg-blue-100 rounded">
                        <i class="fas fa-th-large"></i>
                    </button>
                    <button onclick="toggleView('list')" id="listViewBtn" class="p-2 text-gray-600 hover:bg-gray-100 rounded">
                        <i class="fas fa-list"></i>
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- 报告列表/网格视图 -->
    <div id="reportsContainer">
        <!-- 网格视图 -->
        <div id="gridView" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
            <!-- 报告卡片将通过JavaScript动态生成 -->
        </div>

        <!-- 列表视图 -->
        <div id="listView" class="hidden">
            <div class="card">
                <div class="card-body">
                    <div class="overflow-x-auto">
                        <table class="table">
                            <thead>
                                <tr>
                                    <th>报告名称</th>
                                    <th>分类</th>
                                    <th>创建者</th>
                                    <th>状态</th>
                                    <th>访问量</th>
                                    <th>创建时间</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody id="reportsTableBody">
                                <!-- 表格数据将通过JavaScript动态生成 -->
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 分页 -->
    <div class="flex items-center justify-between mt-6">
        <div class="text-sm text-gray-700">
            显示 <span id="pageInfo">1-12</span> 条，共 <span id="totalCount">89</span> 条记录
        </div>
        <div class="flex items-center space-x-2">
            <button onclick="changePage(-1)" class="btn btn-outline btn-sm">
                <i class="fas fa-chevron-left"></i>
            </button>
            <span id="currentPage" class="px-3 py-1 bg-blue-100 text-blue-800 rounded">1</span>
            <button onclick="changePage(1)" class="btn btn-outline btn-sm">
                <i class="fas fa-chevron-right"></i>
            </button>
        </div>
    </div>

    <script>
        let currentPage = 1;
        const pageSize = 12;
        let reportsData = [];
        let currentView = 'grid';

        document.addEventListener('DOMContentLoaded', function() {
            initReports();
        });

        function initReports() {
            generateMockData();
            renderReports();
            
            // 搜索和筛选功能
            document.getElementById('searchInput').addEventListener('input', renderReports);
            document.getElementById('categoryFilter').addEventListener('change', renderReports);
            document.getElementById('statusFilter').addEventListener('change', renderReports);
        }

        function generateMockData() {
            const categories = ['production', 'quality', 'financial', 'safety'];
            const categoryLabels = { 
                production: '生产报告', 
                quality: '质量报告', 
                financial: '财务报告', 
                safety: '安全报告' 
            };
            const statuses = ['published', 'draft', 'archived'];
            const statusLabels = { 
                published: '已发布', 
                draft: '草稿', 
                archived: '已归档' 
            };
            const creators = ['张三', '李四', '王五', '赵六', '钱七'];
            const reportNames = [
                '月度生产统计报告', '季度质量分析报告', '年度财务总结', '安全事故分析',
                '设备运行状况报告', '环保指标监测', '人员效率分析', '成本控制报告',
                '市场趋势分析', '技术改进建议', '供应链分析', '客户满意度调查'
            ];

            for (let i = 1; i <= 89; i++) {
                const category = categories[Math.floor(Math.random() * categories.length)];
                const status = statuses[Math.floor(Math.random() * statuses.length)];
                const baseName = reportNames[Math.floor(Math.random() * reportNames.length)];
                
                reportsData.push({
                    id: i,
                    name: `${baseName}_${i.toString().padStart(3, '0')}`,
                    category: category,
                    categoryLabel: categoryLabels[category],
                    creator: creators[Math.floor(Math.random() * creators.length)],
                    status: status,
                    statusLabel: statusLabels[status],
                    views: Math.floor(Math.random() * 500 + 10),
                    createTime: new Date(Date.now() - Math.random() * 86400000 * 30).toLocaleString('zh-CN'),
                    thumbnail: `https://via.placeholder.com/300x200/4F46E5/FFFFFF?text=Report+${i}`,
                    description: `这是${baseName}的详细描述，包含了相关的数据分析和可视化图表。`
                });
            }
        }

        function renderReports() {
            const searchTerm = document.getElementById('searchInput').value.toLowerCase();
            const categoryFilter = document.getElementById('categoryFilter').value;
            const statusFilter = document.getElementById('statusFilter').value;
            
            let filteredData = reportsData.filter(item => {
                const matchesSearch = item.name.toLowerCase().includes(searchTerm) || 
                                    item.creator.toLowerCase().includes(searchTerm);
                const matchesCategory = !categoryFilter || item.category === categoryFilter;
                const matchesStatus = !statusFilter || item.status === statusFilter;
                return matchesSearch && matchesCategory && matchesStatus;
            });
            
            const startIndex = (currentPage - 1) * pageSize;
            const endIndex = startIndex + pageSize;
            const pageData = filteredData.slice(startIndex, endIndex);
            
            if (currentView === 'grid') {
                renderGridView(pageData);
            } else {
                renderListView(pageData);
            }
            
            // 更新分页信息
            document.getElementById('pageInfo').textContent = `${startIndex + 1}-${Math.min(endIndex, filteredData.length)}`;
            document.getElementById('totalCount').textContent = filteredData.length;
            document.getElementById('currentPage').textContent = currentPage;
        }

        function renderGridView(data) {
            const container = document.getElementById('gridView');
            container.innerHTML = data.map(report => `
                <div class="card hover:shadow-lg transition-shadow cursor-pointer" onclick="viewReport(${report.id})">
                    <div class="relative">
                        <img src="${report.thumbnail}" alt="${report.name}" class="w-full h-48 object-cover rounded-t-lg">
                        <div class="absolute top-2 right-2">
                            <span class="status-badge ${getStatusClass(report.status)}">${report.statusLabel}</span>
                        </div>
                    </div>
                    <div class="p-4">
                        <h3 class="font-semibold text-gray-900 mb-2 truncate" title="${report.name}">${report.name}</h3>
                        <p class="text-sm text-gray-600 mb-3 line-clamp-2">${report.description}</p>
                        <div class="flex items-center justify-between text-sm text-gray-500">
                            <div class="flex items-center">
                                <i class="fas fa-user mr-1"></i>
                                <span>${report.creator}</span>
                            </div>
                            <div class="flex items-center">
                                <i class="fas fa-eye mr-1"></i>
                                <span>${report.views}</span>
                            </div>
                        </div>
                        <div class="flex items-center justify-between mt-3">
                            <span class="px-2 py-1 bg-blue-100 text-blue-800 rounded text-xs">${report.categoryLabel}</span>
                            <div class="flex items-center space-x-2">
                                <button onclick="event.stopPropagation(); editReport(${report.id})" class="text-green-600 hover:text-green-800">
                                    <i class="fas fa-edit"></i>
                                </button>
                                <button onclick="event.stopPropagation(); shareReport(${report.id})" class="text-blue-600 hover:text-blue-800">
                                    <i class="fas fa-share-alt"></i>
                                </button>
                                <button onclick="event.stopPropagation(); deleteReport(${report.id})" class="text-red-600 hover:text-red-800">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            `).join('');
        }

        function renderListView(data) {
            const tbody = document.getElementById('reportsTableBody');
            tbody.innerHTML = data.map(report => `
                <tr class="hover:bg-gray-50 cursor-pointer" onclick="viewReport(${report.id})">
                    <td class="font-medium">${report.name}</td>
                    <td><span class="px-2 py-1 bg-blue-100 text-blue-800 rounded text-sm">${report.categoryLabel}</span></td>
                    <td>${report.creator}</td>
                    <td><span class="status-badge ${getStatusClass(report.status)}">${report.statusLabel}</span></td>
                    <td>${report.views}</td>
                    <td class="text-sm text-gray-500">${report.createTime}</td>
                    <td>
                        <div class="flex items-center space-x-2">
                            <button onclick="event.stopPropagation(); viewReport(${report.id})" class="text-blue-600 hover:text-blue-800">
                                <i class="fas fa-eye"></i>
                            </button>
                            <button onclick="event.stopPropagation(); editReport(${report.id})" class="text-green-600 hover:text-green-800">
                                <i class="fas fa-edit"></i>
                            </button>
                            <button onclick="event.stopPropagation(); shareReport(${report.id})" class="text-purple-600 hover:text-purple-800">
                                <i class="fas fa-share-alt"></i>
                            </button>
                            <button onclick="event.stopPropagation(); deleteReport(${report.id})" class="text-red-600 hover:text-red-800">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                    </td>
                </tr>
            `).join('');
        }

        function toggleView(view) {
            currentView = view;
            
            const gridView = document.getElementById('gridView');
            const listView = document.getElementById('listView');
            const gridBtn = document.getElementById('gridViewBtn');
            const listBtn = document.getElementById('listViewBtn');
            
            if (view === 'grid') {
                gridView.classList.remove('hidden');
                listView.classList.add('hidden');
                gridBtn.classList.add('text-blue-600', 'bg-blue-100');
                gridBtn.classList.remove('text-gray-600');
                listBtn.classList.remove('text-blue-600', 'bg-blue-100');
                listBtn.classList.add('text-gray-600');
            } else {
                gridView.classList.add('hidden');
                listView.classList.remove('hidden');
                listBtn.classList.add('text-blue-600', 'bg-blue-100');
                listBtn.classList.remove('text-gray-600');
                gridBtn.classList.remove('text-blue-600', 'bg-blue-100');
                gridBtn.classList.add('text-gray-600');
            }
            
            renderReports();
        }

        function getStatusClass(status) {
            const classes = {
                published: 'status-success',
                draft: 'status-warning',
                archived: 'status-info'
            };
            return classes[status] || 'status-info';
        }

        function changePage(direction) {
            const newPage = currentPage + direction;
            if (newPage >= 1 && newPage <= Math.ceil(reportsData.length / pageSize)) {
                currentPage = newPage;
                renderReports();
            }
        }

        function showCreateReportModal() {
            if (parent.showModal) {
                parent.showModal('创建报告', `
                    <form class="space-y-4">
                        <div class="form-group">
                            <label class="form-label">报告名称</label>
                            <input type="text" class="form-input" required>
                        </div>
                        <div class="form-group">
                            <label class="form-label">报告分类</label>
                            <select class="form-select" required>
                                <option value="">请选择</option>
                                <option value="production">生产报告</option>
                                <option value="quality">质量报告</option>
                                <option value="financial">财务报告</option>
                                <option value="safety">安全报告</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label class="form-label">报告描述</label>
                            <textarea class="form-input" rows="3"></textarea>
                        </div>
                        <div class="form-group">
                            <label class="form-label">数据源</label>
                            <select class="form-select" required>
                                <option value="">请选择</option>
                                <option value="mysql">MySQL生产库</option>
                                <option value="hive">Hive数据湖</option>
                                <option value="clickhouse">ClickHouse分析库</option>
                            </select>
                        </div>
                    </form>
                `, [
                    { text: '取消', class: 'btn-outline' },
                    { text: '创建', class: 'btn-primary', onclick: 'createReport()' }
                ]);
            }
        }

        function showTemplateModal() {
            if (parent.showModal) {
                parent.showModal('报告模板', `
                    <div class="grid grid-cols-2 gap-4">
                        <div class="p-4 border rounded-lg cursor-pointer hover:bg-gray-50">
                            <div class="text-center">
                                <i class="fas fa-chart-bar text-3xl text-blue-600 mb-2"></i>
                                <div class="font-medium">生产统计模板</div>
                                <div class="text-sm text-gray-500">包含产量、效率等指标</div>
                            </div>
                        </div>
                        <div class="p-4 border rounded-lg cursor-pointer hover:bg-gray-50">
                            <div class="text-center">
                                <i class="fas fa-check-circle text-3xl text-green-600 mb-2"></i>
                                <div class="font-medium">质量分析模板</div>
                                <div class="text-sm text-gray-500">质量评估和改进建议</div>
                            </div>
                        </div>
                        <div class="p-4 border rounded-lg cursor-pointer hover:bg-gray-50">
                            <div class="text-center">
                                <i class="fas fa-dollar-sign text-3xl text-yellow-600 mb-2"></i>
                                <div class="font-medium">财务报告模板</div>
                                <div class="text-sm text-gray-500">成本分析和收益统计</div>
                            </div>
                        </div>
                        <div class="p-4 border rounded-lg cursor-pointer hover:bg-gray-50">
                            <div class="text-center">
                                <i class="fas fa-shield-alt text-3xl text-red-600 mb-2"></i>
                                <div class="font-medium">安全监控模板</div>
                                <div class="text-sm text-gray-500">安全指标和事故分析</div>
                            </div>
                        </div>
                    </div>
                `);
            }
        }

        function createReport() {
            if (parent.showToast) {
                parent.showToast('报告创建成功', 'success');
            }
        }

        function viewReport(id) {
            if (parent.showToast) {
                parent.showToast('正在打开报告...', 'info');
            }
        }

        function editReport(id) {
            if (parent.showToast) {
                parent.showToast('编辑功能开发中', 'info');
            }
        }

        function shareReport(id) {
            if (parent.showToast) {
                parent.showToast('分享链接已复制到剪贴板', 'success');
            }
        }

        function deleteReport(id) {
            if (parent.showConfirm) {
                parent.showConfirm('确定要删除这个报告吗？', function() {
                    if (parent.showToast) {
                        parent.showToast('报告删除成功', 'success');
                    }
                });
            }
        }
    </script>
</body>
</html>
