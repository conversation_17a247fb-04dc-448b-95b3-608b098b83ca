<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>智能体管理</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <link rel="stylesheet" href="../css/style.css">
</head>
<body class="bg-gray-100 p-6">
    <!-- 页面头部 -->
    <div class="mb-6">
        <div class="flex items-center justify-between">
            <div>
                <h1 class="text-2xl font-bold text-gray-900">智能体管理</h1>
                <p class="text-gray-600 mt-1">AI智能体配置与管理中心</p>
            </div>
            <div class="flex items-center space-x-4">
                <button onclick="showCreateAgentModal()" class="btn btn-primary">
                    <i class="fas fa-plus mr-2"></i>创建智能体
                </button>
                <button onclick="importAgents()" class="btn btn-outline">
                    <i class="fas fa-upload mr-2"></i>导入配置
                </button>
            </div>
        </div>
    </div>

    <!-- 智能体概览 -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <div class="card">
            <div class="card-body">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-600">总智能体数</p>
                        <p class="text-2xl font-bold text-gray-900">15</p>
                    </div>
                    <div class="p-3 bg-blue-100 rounded-full">
                        <i class="fas fa-robot text-blue-600 text-xl"></i>
                    </div>
                </div>
            </div>
        </div>
        <div class="card">
            <div class="card-body">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-600">活跃智能体</p>
                        <p class="text-2xl font-bold text-green-600">12</p>
                    </div>
                    <div class="p-3 bg-green-100 rounded-full">
                        <i class="fas fa-check-circle text-green-600 text-xl"></i>
                    </div>
                </div>
            </div>
        </div>
        <div class="card">
            <div class="card-body">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-600">今日调用次数</p>
                        <p class="text-2xl font-bold text-purple-600">2,847</p>
                    </div>
                    <div class="p-3 bg-purple-100 rounded-full">
                        <i class="fas fa-chart-line text-purple-600 text-xl"></i>
                    </div>
                </div>
            </div>
        </div>
        <div class="card">
            <div class="card-body">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-600">平均响应时间</p>
                        <p class="text-2xl font-bold text-orange-600">1.2s</p>
                    </div>
                    <div class="p-3 bg-orange-100 rounded-full">
                        <i class="fas fa-clock text-orange-600 text-xl"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 智能体性能图表 -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
        <div class="card">
            <div class="card-body">
                <h3 class="text-lg font-semibold mb-4">智能体调用趋势</h3>
                <div class="chart-container">
                    <canvas id="callTrendChart"></canvas>
                </div>
            </div>
        </div>
        <div class="card">
            <div class="card-body">
                <h3 class="text-lg font-semibold mb-4">智能体类型分布</h3>
                <div class="chart-container">
                    <canvas id="agentTypeChart"></canvas>
                </div>
            </div>
        </div>
    </div>

    <!-- 智能体列表 -->
    <div class="card">
        <div class="card-body">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg font-semibold">智能体列表</h3>
                <div class="flex items-center space-x-4">
                    <div class="relative">
                        <input type="text" id="searchInput" placeholder="搜索智能体..." 
                               class="form-input pl-10 pr-4 py-2 w-64">
                        <i class="fas fa-search absolute left-3 top-3 text-gray-400"></i>
                    </div>
                    <select id="typeFilter" class="form-select">
                        <option value="">全部类型</option>
                        <option value="analysis">数据分析</option>
                        <option value="mining">数据挖掘</option>
                        <option value="prediction">预测分析</option>
                        <option value="nlp">自然语言处理</option>
                        <option value="vision">计算机视觉</option>
                    </select>
                    <select id="statusFilter" class="form-select">
                        <option value="">全部状态</option>
                        <option value="active">运行中</option>
                        <option value="inactive">已停止</option>
                        <option value="training">训练中</option>
                        <option value="error">异常</option>
                    </select>
                </div>
            </div>
            
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6" id="agentGrid">
                <!-- 智能体卡片将通过JavaScript动态加载 -->
            </div>
        </div>
    </div>

    <!-- 智能体详情模态框 -->
    <div id="agentDetailModal" class="fixed inset-0 bg-black bg-opacity-50 hidden z-50">
        <div class="flex items-center justify-center min-h-screen p-4">
            <div class="bg-white rounded-lg max-w-4xl w-full max-h-screen overflow-y-auto">
                <div class="p-6">
                    <div class="flex items-center justify-between mb-4">
                        <h3 class="text-xl font-semibold">智能体详情</h3>
                        <button onclick="closeAgentDetail()" class="text-gray-400 hover:text-gray-600">
                            <i class="fas fa-times text-xl"></i>
                        </button>
                    </div>
                    <div id="agentDetailContent">
                        <!-- 详情内容将动态加载 -->
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        let agentData = [];

        document.addEventListener('DOMContentLoaded', function() {
            initAgents();
        });

        function initAgents() {
            generateMockData();
            createCharts();
            renderAgentGrid();
            
            // 搜索和筛选功能
            document.getElementById('searchInput').addEventListener('input', renderAgentGrid);
            document.getElementById('typeFilter').addEventListener('change', renderAgentGrid);
            document.getElementById('statusFilter').addEventListener('change', renderAgentGrid);
        }

        function generateMockData() {
            const types = ['analysis', 'mining', 'prediction', 'nlp', 'vision'];
            const typeLabels = { 
                analysis: '数据分析', 
                mining: '数据挖掘', 
                prediction: '预测分析', 
                nlp: '自然语言处理', 
                vision: '计算机视觉' 
            };
            const statuses = ['active', 'inactive', 'training', 'error'];
            const statusLabels = { 
                active: '运行中', 
                inactive: '已停止', 
                training: '训练中', 
                error: '异常' 
            };
            const agentNames = [
                '矿产资源分析师', '生产数据挖掘器', '安全预警系统', '质量检测助手', '成本优化顾问',
                '环境监测专家', '设备维护助理', '市场分析师', '风险评估专家', '智能问答助手',
                '报告生成器', '数据清洗工具', '异常检测器', '趋势预测师', '决策支持系统'
            ];

            for (let i = 0; i < 15; i++) {
                const type = types[Math.floor(Math.random() * types.length)];
                const status = statuses[Math.floor(Math.random() * statuses.length)];
                const accuracy = Math.floor(Math.random() * 20 + 80); // 80-100%
                const callCount = Math.floor(Math.random() * 1000 + 100);
                const responseTime = (Math.random() * 2 + 0.5).toFixed(1); // 0.5-2.5s
                
                agentData.push({
                    id: i + 1,
                    name: agentNames[i],
                    type: type,
                    typeLabel: typeLabels[type],
                    status: status,
                    statusLabel: statusLabels[status],
                    description: `专业的${typeLabels[type]}智能体，提供高效准确的数据处理服务`,
                    accuracy: accuracy,
                    callCount: callCount,
                    responseTime: responseTime,
                    version: `v${Math.floor(Math.random() * 3 + 1)}.${Math.floor(Math.random() * 10)}.${Math.floor(Math.random() * 10)}`,
                    createTime: new Date(Date.now() - Math.random() * 86400000 * 30).toLocaleString('zh-CN'),
                    lastUpdate: new Date(Date.now() - Math.random() * 86400000 * 7).toLocaleString('zh-CN'),
                    avatar: `https://ui-avatars.com/api/?name=${encodeURIComponent(agentNames[i])}&background=random&color=fff`
                });
            }
        }

        function createCharts() {
            // 智能体调用趋势图
            const trendCtx = document.getElementById('callTrendChart').getContext('2d');
            new Chart(trendCtx, {
                type: 'line',
                data: {
                    labels: ['00:00', '04:00', '08:00', '12:00', '16:00', '20:00'],
                    datasets: [{
                        label: '总调用次数',
                        data: [120, 89, 245, 389, 456, 298],
                        borderColor: 'rgb(59, 130, 246)',
                        backgroundColor: 'rgba(59, 130, 246, 0.1)',
                        tension: 0.4,
                        fill: true
                    }, {
                        label: '成功调用',
                        data: [115, 85, 238, 375, 441, 289],
                        borderColor: 'rgb(34, 197, 94)',
                        backgroundColor: 'rgba(34, 197, 94, 0.1)',
                        tension: 0.4
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: { legend: { position: 'bottom' } }
                }
            });

            // 智能体类型分布图
            const typeCtx = document.getElementById('agentTypeChart').getContext('2d');
            new Chart(typeCtx, {
                type: 'doughnut',
                data: {
                    labels: ['数据分析', '数据挖掘', '预测分析', '自然语言处理', '计算机视觉'],
                    datasets: [{
                        data: [30, 25, 20, 15, 10],
                        backgroundColor: ['#3b82f6', '#10b981', '#8b5cf6', '#f59e0b', '#ef4444']
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: { legend: { position: 'bottom' } }
                }
            });
        }

        function renderAgentGrid() {
            const searchTerm = document.getElementById('searchInput').value.toLowerCase();
            const typeFilter = document.getElementById('typeFilter').value;
            const statusFilter = document.getElementById('statusFilter').value;
            
            let filteredData = agentData.filter(item => {
                const matchesSearch = item.name.toLowerCase().includes(searchTerm) || 
                                    item.description.toLowerCase().includes(searchTerm);
                const matchesType = !typeFilter || item.type === typeFilter;
                const matchesStatus = !statusFilter || item.status === statusFilter;
                return matchesSearch && matchesType && matchesStatus;
            });
            
            const grid = document.getElementById('agentGrid');
            grid.innerHTML = filteredData.map(agent => `
                <div class="card hover:shadow-lg transition-shadow cursor-pointer" onclick="showAgentDetail(${agent.id})">
                    <div class="card-body">
                        <div class="flex items-center space-x-3 mb-4">
                            <img src="${agent.avatar}" alt="${agent.name}" class="w-12 h-12 rounded-full">
                            <div class="flex-1">
                                <h4 class="font-semibold text-gray-900">${agent.name}</h4>
                                <p class="text-sm text-gray-500">${agent.version}</p>
                            </div>
                            <span class="status-badge ${getStatusClass(agent.status)}">${agent.statusLabel}</span>
                        </div>
                        
                        <div class="mb-4">
                            <span class="px-2 py-1 bg-blue-100 text-blue-800 rounded text-sm">${agent.typeLabel}</span>
                        </div>
                        
                        <p class="text-sm text-gray-600 mb-4 line-clamp-2">${agent.description}</p>
                        
                        <div class="grid grid-cols-2 gap-4 text-sm">
                            <div>
                                <div class="text-gray-500">准确率</div>
                                <div class="font-medium text-green-600">${agent.accuracy}%</div>
                            </div>
                            <div>
                                <div class="text-gray-500">调用次数</div>
                                <div class="font-medium">${agent.callCount.toLocaleString()}</div>
                            </div>
                            <div>
                                <div class="text-gray-500">响应时间</div>
                                <div class="font-medium">${agent.responseTime}s</div>
                            </div>
                            <div>
                                <div class="text-gray-500">最后更新</div>
                                <div class="font-medium text-xs">${agent.lastUpdate.split(' ')[0]}</div>
                            </div>
                        </div>
                        
                        <div class="flex items-center justify-between mt-4 pt-4 border-t">
                            <div class="flex items-center space-x-2">
                                <button onclick="event.stopPropagation(); testAgent(${agent.id})" 
                                        class="text-blue-600 hover:text-blue-800" title="测试">
                                    <i class="fas fa-play"></i>
                                </button>
                                <button onclick="event.stopPropagation(); editAgent(${agent.id})" 
                                        class="text-green-600 hover:text-green-800" title="编辑">
                                    <i class="fas fa-edit"></i>
                                </button>
                                <button onclick="event.stopPropagation(); cloneAgent(${agent.id})" 
                                        class="text-purple-600 hover:text-purple-800" title="克隆">
                                    <i class="fas fa-copy"></i>
                                </button>
                            </div>
                            ${agent.status === 'active' ? 
                                `<button onclick="event.stopPropagation(); stopAgent(${agent.id})" 
                                         class="text-red-600 hover:text-red-800" title="停止">
                                    <i class="fas fa-stop"></i>
                                </button>` :
                                `<button onclick="event.stopPropagation(); startAgent(${agent.id})" 
                                         class="text-green-600 hover:text-green-800" title="启动">
                                    <i class="fas fa-play"></i>
                                </button>`
                            }
                        </div>
                    </div>
                </div>
            `).join('');
        }

        function getStatusClass(status) {
            const classes = {
                active: 'status-success',
                inactive: 'status-warning',
                training: 'status-info',
                error: 'status-error'
            };
            return classes[status] || 'status-info';
        }

        function showAgentDetail(id) {
            const agent = agentData.find(item => item.id === id);
            if (!agent) return;
            
            const modal = document.getElementById('agentDetailModal');
            const content = document.getElementById('agentDetailContent');
            
            content.innerHTML = `
                <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
                    <!-- 基本信息 -->
                    <div class="lg:col-span-2 space-y-6">
                        <div class="flex items-center space-x-4">
                            <img src="${agent.avatar}" alt="${agent.name}" class="w-16 h-16 rounded-full">
                            <div>
                                <h4 class="text-xl font-semibold">${agent.name}</h4>
                                <p class="text-gray-600">${agent.typeLabel} • ${agent.version}</p>
                                <span class="status-badge ${getStatusClass(agent.status)} mt-2">${agent.statusLabel}</span>
                            </div>
                        </div>
                        
                        <div>
                            <h5 class="font-medium mb-2">描述</h5>
                            <p class="text-gray-600">${agent.description}</p>
                        </div>
                        
                        <div>
                            <h5 class="font-medium mb-3">性能指标</h5>
                            <div class="grid grid-cols-2 gap-4">
                                <div class="p-4 bg-green-50 rounded-lg">
                                    <div class="text-2xl font-bold text-green-600">${agent.accuracy}%</div>
                                    <div class="text-sm text-green-800">准确率</div>
                                </div>
                                <div class="p-4 bg-blue-50 rounded-lg">
                                    <div class="text-2xl font-bold text-blue-600">${agent.callCount.toLocaleString()}</div>
                                    <div class="text-sm text-blue-800">总调用次数</div>
                                </div>
                                <div class="p-4 bg-purple-50 rounded-lg">
                                    <div class="text-2xl font-bold text-purple-600">${agent.responseTime}s</div>
                                    <div class="text-sm text-purple-800">平均响应时间</div>
                                </div>
                                <div class="p-4 bg-orange-50 rounded-lg">
                                    <div class="text-2xl font-bold text-orange-600">99.5%</div>
                                    <div class="text-sm text-orange-800">可用性</div>
                                </div>
                            </div>
                        </div>
                        
                        <div>
                            <h5 class="font-medium mb-3">配置参数</h5>
                            <div class="bg-gray-50 p-4 rounded-lg">
                                <div class="grid grid-cols-2 gap-4 text-sm">
                                    <div><strong>模型类型：</strong>Transformer</div>
                                    <div><strong>训练数据：</strong>10M+ 样本</div>
                                    <div><strong>最大并发：</strong>100</div>
                                    <div><strong>超时时间：</strong>30s</div>
                                    <div><strong>内存限制：</strong>2GB</div>
                                    <div><strong>CPU核数：</strong>4</div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 操作面板 -->
                    <div class="space-y-6">
                        <div>
                            <h5 class="font-medium mb-3">快速操作</h5>
                            <div class="space-y-2">
                                <button onclick="testAgent(${agent.id})" class="w-full btn btn-primary">
                                    <i class="fas fa-play mr-2"></i>测试智能体
                                </button>
                                <button onclick="editAgent(${agent.id})" class="w-full btn btn-outline">
                                    <i class="fas fa-edit mr-2"></i>编辑配置
                                </button>
                                <button onclick="viewLogs(${agent.id})" class="w-full btn btn-outline">
                                    <i class="fas fa-file-alt mr-2"></i>查看日志
                                </button>
                                <button onclick="exportAgent(${agent.id})" class="w-full btn btn-outline">
                                    <i class="fas fa-download mr-2"></i>导出配置
                                </button>
                            </div>
                        </div>
                        
                        <div>
                            <h5 class="font-medium mb-3">基本信息</h5>
                            <div class="space-y-2 text-sm">
                                <div><strong>创建时间：</strong>${agent.createTime}</div>
                                <div><strong>最后更新：</strong>${agent.lastUpdate}</div>
                                <div><strong>创建者：</strong>系统管理员</div>
                                <div><strong>标签：</strong>
                                    <span class="px-2 py-1 bg-gray-100 rounded text-xs mr-1">AI</span>
                                    <span class="px-2 py-1 bg-gray-100 rounded text-xs mr-1">${agent.typeLabel}</span>
                                </div>
                            </div>
                        </div>
                        
                        <div>
                            <h5 class="font-medium mb-3">API接口</h5>
                            <div class="bg-gray-50 p-3 rounded text-xs font-mono">
                                POST /api/agents/${agent.id}/invoke<br>
                                GET /api/agents/${agent.id}/status<br>
                                PUT /api/agents/${agent.id}/config
                            </div>
                        </div>
                    </div>
                </div>
            `;
            
            modal.classList.remove('hidden');
        }

        function closeAgentDetail() {
            document.getElementById('agentDetailModal').classList.add('hidden');
        }

        function showCreateAgentModal() {
            if (parent.showModal) {
                parent.showModal('创建智能体', `
                    <form class="space-y-4">
                        <div class="grid grid-cols-2 gap-4">
                            <div class="form-group">
                                <label class="form-label">智能体名称</label>
                                <input type="text" class="form-input" required>
                            </div>
                            <div class="form-group">
                                <label class="form-label">智能体类型</label>
                                <select class="form-select" required>
                                    <option value="">请选择</option>
                                    <option value="analysis">数据分析</option>
                                    <option value="mining">数据挖掘</option>
                                    <option value="prediction">预测分析</option>
                                    <option value="nlp">自然语言处理</option>
                                    <option value="vision">计算机视觉</option>
                                </select>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="form-label">描述</label>
                            <textarea class="form-input" rows="3" required></textarea>
                        </div>
                        <div class="grid grid-cols-2 gap-4">
                            <div class="form-group">
                                <label class="form-label">模型文件</label>
                                <input type="file" class="form-input" accept=".pkl,.h5,.onnx">
                            </div>
                            <div class="form-group">
                                <label class="form-label">版本号</label>
                                <input type="text" class="form-input" placeholder="v1.0.0" required>
                            </div>
                        </div>
                        <div class="grid grid-cols-3 gap-4">
                            <div class="form-group">
                                <label class="form-label">最大并发数</label>
                                <input type="number" class="form-input" value="10" min="1" max="1000">
                            </div>
                            <div class="form-group">
                                <label class="form-label">超时时间(秒)</label>
                                <input type="number" class="form-input" value="30" min="1" max="300">
                            </div>
                            <div class="form-group">
                                <label class="form-label">内存限制(GB)</label>
                                <input type="number" class="form-input" value="2" min="1" max="32">
                            </div>
                        </div>
                    </form>
                `, [
                    { text: '取消', class: 'btn-outline' },
                    { text: '创建', class: 'btn-primary', onclick: 'createAgent()' }
                ]);
            }
        }

        function createAgent() {
            if (parent.showToast) {
                parent.showToast('智能体创建成功', 'success');
            }
        }

        function testAgent(id) {
            if (parent.showToast) {
                parent.showToast('智能体测试已启动', 'info');
                setTimeout(() => {
                    parent.showToast('测试完成，响应正常', 'success');
                }, 2000);
            }
        }

        function editAgent(id) {
            if (parent.showToast) {
                parent.showToast('编辑功能开发中', 'info');
            }
        }

        function cloneAgent(id) {
            if (parent.showConfirm) {
                parent.showConfirm('确定要克隆这个智能体吗？', function() {
                    if (parent.showToast) {
                        parent.showToast('智能体克隆成功', 'success');
                    }
                });
            }
        }

        function startAgent(id) {
            if (parent.showToast) {
                parent.showToast('智能体启动中...', 'info');
                setTimeout(() => {
                    parent.showToast('智能体启动成功', 'success');
                }, 1500);
            }
        }

        function stopAgent(id) {
            if (parent.showConfirm) {
                parent.showConfirm('确定要停止这个智能体吗？', function() {
                    if (parent.showToast) {
                        parent.showToast('智能体已停止', 'warning');
                    }
                });
            }
        }

        function viewLogs(id) {
            if (parent.showToast) {
                parent.showToast('日志查看功能开发中', 'info');
            }
        }

        function exportAgent(id) {
            if (parent.showToast) {
                parent.showToast('配置导出功能开发中', 'info');
            }
        }

        function importAgents() {
            if (parent.showToast) {
                parent.showToast('配置导入功能开发中', 'info');
            }
        }

        // 点击模态框外部关闭
        document.getElementById('agentDetailModal').addEventListener('click', function(e) {
            if (e.target === this) {
                closeAgentDetail();
            }
        });
    </script>
</body>
</html>
