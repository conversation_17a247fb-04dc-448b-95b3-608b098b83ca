<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>数据集成</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <link rel="stylesheet" href="../css/style.css">
</head>
<body class="bg-gray-100 p-6">
    <!-- 页面头部 -->
    <div class="mb-6">
        <div class="flex items-center justify-between">
            <div>
                <h1 class="text-2xl font-bold text-gray-900">数据集成</h1>
                <p class="text-gray-600 mt-1">管理和监控数据集成任务</p>
            </div>
            <div class="flex items-center space-x-4">
                <button onclick="showCreateTaskModal()" class="btn btn-primary">
                    <i class="fas fa-plus mr-2"></i>创建集成任务
                </button>
                <button onclick="refreshTasks()" class="btn btn-outline">
                    <i class="fas fa-sync-alt mr-2"></i>刷新
                </button>
            </div>
        </div>
    </div>

    <!-- 集成任务统计 -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <div class="card">
            <div class="card-body">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-600">总任务数</p>
                        <p class="text-2xl font-bold text-gray-900">156</p>
                    </div>
                    <div class="p-3 bg-blue-100 rounded-full">
                        <i class="fas fa-tasks text-blue-600 text-xl"></i>
                    </div>
                </div>
            </div>
        </div>
        <div class="card">
            <div class="card-body">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-600">运行中</p>
                        <p class="text-2xl font-bold text-green-600">23</p>
                    </div>
                    <div class="p-3 bg-green-100 rounded-full">
                        <i class="fas fa-play-circle text-green-600 text-xl"></i>
                    </div>
                </div>
            </div>
        </div>
        <div class="card">
            <div class="card-body">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-600">成功率</p>
                        <p class="text-2xl font-bold text-blue-600">94.2%</p>
                    </div>
                    <div class="p-3 bg-yellow-100 rounded-full">
                        <i class="fas fa-chart-line text-yellow-600 text-xl"></i>
                    </div>
                </div>
            </div>
        </div>
        <div class="card">
            <div class="card-body">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-600">今日处理量</p>
                        <p class="text-2xl font-bold text-purple-600">2.3M</p>
                    </div>
                    <div class="p-3 bg-purple-100 rounded-full">
                        <i class="fas fa-database text-purple-600 text-xl"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 任务执行趋势图表 -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
        <div class="card">
            <div class="card-body">
                <h3 class="text-lg font-semibold mb-4">任务执行趋势</h3>
                <div class="chart-container">
                    <canvas id="taskTrendChart"></canvas>
                </div>
            </div>
        </div>
        <div class="card">
            <div class="card-body">
                <h3 class="text-lg font-semibold mb-4">数据源分布</h3>
                <div class="chart-container">
                    <canvas id="dataSourceChart"></canvas>
                </div>
            </div>
        </div>
    </div>

    <!-- 集成任务列表 -->
    <div class="card">
        <div class="card-body">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg font-semibold">集成任务列表</h3>
                <div class="flex items-center space-x-4">
                    <div class="relative">
                        <input type="text" id="searchInput" placeholder="搜索任务..." 
                               class="form-input pl-10 pr-4 py-2 w-64">
                        <i class="fas fa-search absolute left-3 top-3 text-gray-400"></i>
                    </div>
                    <select id="statusFilter" class="form-select">
                        <option value="">全部状态</option>
                        <option value="running">运行中</option>
                        <option value="success">成功</option>
                        <option value="failed">失败</option>
                        <option value="stopped">已停止</option>
                    </select>
                    <select id="typeFilter" class="form-select">
                        <option value="">全部类型</option>
                        <option value="realtime">实时同步</option>
                        <option value="batch">批量同步</option>
                        <option value="incremental">增量同步</option>
                    </select>
                </div>
            </div>
            
            <div class="overflow-x-auto">
                <table class="table" id="taskTable">
                    <thead>
                        <tr>
                            <th>任务名称</th>
                            <th>类型</th>
                            <th>源数据源</th>
                            <th>目标数据源</th>
                            <th>状态</th>
                            <th>进度</th>
                            <th>最后执行</th>
                            <th>下次执行</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody id="taskTableBody">
                        <!-- 数据将通过JavaScript动态加载 -->
                    </tbody>
                </table>
            </div>
            
            <!-- 分页 -->
            <div class="flex items-center justify-between mt-4">
                <div class="text-sm text-gray-700">
                    显示 <span id="pageInfo">1-10</span> 条，共 <span id="totalCount">156</span> 条记录
                </div>
                <div class="flex items-center space-x-2">
                    <button onclick="changePage(-1)" class="btn btn-outline btn-sm">
                        <i class="fas fa-chevron-left"></i>
                    </button>
                    <span id="currentPage" class="px-3 py-1 bg-blue-100 text-blue-800 rounded">1</span>
                    <button onclick="changePage(1)" class="btn btn-outline btn-sm">
                        <i class="fas fa-chevron-right"></i>
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script>
        let currentPage = 1;
        const pageSize = 10;
        let taskData = [];

        document.addEventListener('DOMContentLoaded', function() {
            initIntegration();
        });

        function initIntegration() {
            generateMockData();
            createCharts();
            renderTaskTable();
            
            // 搜索和筛选功能
            document.getElementById('searchInput').addEventListener('input', renderTaskTable);
            document.getElementById('statusFilter').addEventListener('change', renderTaskTable);
            document.getElementById('typeFilter').addEventListener('change', renderTaskTable);
        }

        function generateMockData() {
            const taskTypes = ['realtime', 'batch', 'incremental'];
            const typeLabels = { realtime: '实时同步', batch: '批量同步', incremental: '增量同步' };
            const statuses = ['running', 'success', 'failed', 'stopped'];
            const statusLabels = { running: '运行中', success: '成功', failed: '失败', stopped: '已停止' };
            const sources = ['MySQL生产库', 'Oracle数据仓库', 'Kafka消息队列', '文件服务器', 'MongoDB集合'];
            const targets = ['Hive数据湖', 'ClickHouse分析库', 'Elasticsearch搜索', 'Redis缓存'];
            
            for (let i = 1; i <= 156; i++) {
                const type = taskTypes[Math.floor(Math.random() * taskTypes.length)];
                const status = statuses[Math.floor(Math.random() * statuses.length)];
                const progress = status === 'running' ? Math.floor(Math.random() * 100) : 
                                status === 'success' ? 100 : 
                                status === 'failed' ? Math.floor(Math.random() * 50) : 0;
                
                taskData.push({
                    id: i,
                    name: `数据集成任务_${i.toString().padStart(3, '0')}`,
                    type: type,
                    typeLabel: typeLabels[type],
                    source: sources[Math.floor(Math.random() * sources.length)],
                    target: targets[Math.floor(Math.random() * targets.length)],
                    status: status,
                    statusLabel: statusLabels[status],
                    progress: progress,
                    lastRun: new Date(Date.now() - Math.random() * 86400000 * 7).toLocaleString('zh-CN'),
                    nextRun: status === 'running' || status === 'stopped' ? '-' : 
                            new Date(Date.now() + Math.random() * 86400000).toLocaleString('zh-CN')
                });
            }
        }

        function createCharts() {
            // 任务执行趋势图
            const trendCtx = document.getElementById('taskTrendChart').getContext('2d');
            new Chart(trendCtx, {
                type: 'line',
                data: {
                    labels: ['周一', '周二', '周三', '周四', '周五', '周六', '周日'],
                    datasets: [{
                        label: '成功任务',
                        data: [45, 52, 48, 61, 55, 42, 38],
                        borderColor: 'rgb(34, 197, 94)',
                        backgroundColor: 'rgba(34, 197, 94, 0.1)',
                        tension: 0.4
                    }, {
                        label: '失败任务',
                        data: [3, 2, 4, 2, 3, 5, 4],
                        borderColor: 'rgb(239, 68, 68)',
                        backgroundColor: 'rgba(239, 68, 68, 0.1)',
                        tension: 0.4
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: { legend: { position: 'bottom' } }
                }
            });

            // 数据源分布图
            const sourceCtx = document.getElementById('dataSourceChart').getContext('2d');
            new Chart(sourceCtx, {
                type: 'doughnut',
                data: {
                    labels: ['MySQL', 'Oracle', 'Kafka', '文件系统', 'MongoDB'],
                    datasets: [{
                        data: [35, 25, 20, 12, 8],
                        backgroundColor: ['#3b82f6', '#10b981', '#f59e0b', '#8b5cf6', '#ef4444']
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: { legend: { position: 'bottom' } }
                }
            });
        }

        function renderTaskTable() {
            const searchTerm = document.getElementById('searchInput').value.toLowerCase();
            const statusFilter = document.getElementById('statusFilter').value;
            const typeFilter = document.getElementById('typeFilter').value;
            
            let filteredData = taskData.filter(item => {
                const matchesSearch = item.name.toLowerCase().includes(searchTerm) || 
                                    item.source.toLowerCase().includes(searchTerm) ||
                                    item.target.toLowerCase().includes(searchTerm);
                const matchesStatus = !statusFilter || item.status === statusFilter;
                const matchesType = !typeFilter || item.type === typeFilter;
                return matchesSearch && matchesStatus && matchesType;
            });
            
            const startIndex = (currentPage - 1) * pageSize;
            const endIndex = startIndex + pageSize;
            const pageData = filteredData.slice(startIndex, endIndex);
            
            const tbody = document.getElementById('taskTableBody');
            tbody.innerHTML = pageData.map(item => `
                <tr>
                    <td class="font-medium">${item.name}</td>
                    <td><span class="px-2 py-1 bg-blue-100 text-blue-800 rounded text-sm">${item.typeLabel}</span></td>
                    <td>${item.source}</td>
                    <td>${item.target}</td>
                    <td><span class="status-badge ${getStatusClass(item.status)}">${item.statusLabel}</span></td>
                    <td>
                        <div class="flex items-center">
                            <div class="w-16 bg-gray-200 rounded-full h-2 mr-2">
                                <div class="bg-blue-600 h-2 rounded-full" style="width: ${item.progress}%"></div>
                            </div>
                            <span class="text-sm">${item.progress}%</span>
                        </div>
                    </td>
                    <td class="text-sm text-gray-500">${item.lastRun}</td>
                    <td class="text-sm text-gray-500">${item.nextRun}</td>
                    <td>
                        <div class="flex items-center space-x-2">
                            <button onclick="viewTaskDetails(${item.id})" class="text-blue-600 hover:text-blue-800" title="查看详情">
                                <i class="fas fa-eye"></i>
                            </button>
                            <button onclick="editTask(${item.id})" class="text-green-600 hover:text-green-800" title="编辑">
                                <i class="fas fa-edit"></i>
                            </button>
                            ${item.status === 'running' ? 
                                `<button onclick="stopTask(${item.id})" class="text-orange-600 hover:text-orange-800" title="停止">
                                    <i class="fas fa-stop"></i>
                                </button>` :
                                `<button onclick="startTask(${item.id})" class="text-green-600 hover:text-green-800" title="启动">
                                    <i class="fas fa-play"></i>
                                </button>`
                            }
                            <button onclick="deleteTask(${item.id})" class="text-red-600 hover:text-red-800" title="删除">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                    </td>
                </tr>
            `).join('');
            
            // 更新分页信息
            document.getElementById('pageInfo').textContent = `${startIndex + 1}-${Math.min(endIndex, filteredData.length)}`;
            document.getElementById('totalCount').textContent = filteredData.length;
            document.getElementById('currentPage').textContent = currentPage;
        }

        function getStatusClass(status) {
            const classes = {
                running: 'status-info',
                success: 'status-success',
                failed: 'status-error',
                stopped: 'status-warning'
            };
            return classes[status] || 'status-info';
        }

        function changePage(direction) {
            const newPage = currentPage + direction;
            if (newPage >= 1 && newPage <= Math.ceil(taskData.length / pageSize)) {
                currentPage = newPage;
                renderTaskTable();
            }
        }

        function refreshTasks() {
            if (parent.showToast) {
                parent.showToast('任务列表刷新成功', 'success');
            }
            renderTaskTable();
        }

        function showCreateTaskModal() {
            if (parent.showModal) {
                parent.showModal('创建集成任务', `
                    <form class="space-y-4">
                        <div class="form-group">
                            <label class="form-label">任务名称</label>
                            <input type="text" class="form-input" required>
                        </div>
                        <div class="form-group">
                            <label class="form-label">任务类型</label>
                            <select class="form-select" required>
                                <option value="">请选择</option>
                                <option value="realtime">实时同步</option>
                                <option value="batch">批量同步</option>
                                <option value="incremental">增量同步</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label class="form-label">源数据源</label>
                            <select class="form-select" required>
                                <option value="">请选择</option>
                                <option value="mysql">MySQL生产库</option>
                                <option value="oracle">Oracle数据仓库</option>
                                <option value="kafka">Kafka消息队列</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label class="form-label">目标数据源</label>
                            <select class="form-select" required>
                                <option value="">请选择</option>
                                <option value="hive">Hive数据湖</option>
                                <option value="clickhouse">ClickHouse分析库</option>
                                <option value="elasticsearch">Elasticsearch搜索</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label class="form-label">调度策略</label>
                            <select class="form-select" required>
                                <option value="">请选择</option>
                                <option value="manual">手动执行</option>
                                <option value="cron">定时执行</option>
                                <option value="trigger">事件触发</option>
                            </select>
                        </div>
                    </form>
                `, [
                    { text: '取消', class: 'btn-outline' },
                    { text: '创建', class: 'btn-primary', onclick: 'createTask()' }
                ]);
            }
        }

        function createTask() {
            if (parent.showToast) {
                parent.showToast('集成任务创建成功', 'success');
            }
        }

        function viewTaskDetails(id) {
            const task = taskData.find(item => item.id === id);
            if (parent.showModal && task) {
                parent.showModal('任务详情', `
                    <div class="space-y-3">
                        <div><strong>任务名称：</strong>${task.name}</div>
                        <div><strong>任务类型：</strong>${task.typeLabel}</div>
                        <div><strong>源数据源：</strong>${task.source}</div>
                        <div><strong>目标数据源：</strong>${task.target}</div>
                        <div><strong>当前状态：</strong><span class="status-badge ${getStatusClass(task.status)}">${task.statusLabel}</span></div>
                        <div><strong>执行进度：</strong>${task.progress}%</div>
                        <div><strong>最后执行：</strong>${task.lastRun}</div>
                        <div><strong>下次执行：</strong>${task.nextRun}</div>
                    </div>
                `);
            }
        }

        function editTask(id) {
            if (parent.showToast) {
                parent.showToast('编辑功能开发中', 'info');
            }
        }

        function startTask(id) {
            if (parent.showConfirm) {
                parent.showConfirm('确定要启动这个任务吗？', function() {
                    if (parent.showToast) {
                        parent.showToast('任务启动成功', 'success');
                    }
                });
            }
        }

        function stopTask(id) {
            if (parent.showConfirm) {
                parent.showConfirm('确定要停止这个任务吗？', function() {
                    if (parent.showToast) {
                        parent.showToast('任务已停止', 'warning');
                    }
                });
            }
        }

        function deleteTask(id) {
            if (parent.showConfirm) {
                parent.showConfirm('确定要删除这个任务吗？删除后无法恢复。', function() {
                    if (parent.showToast) {
                        parent.showToast('任务删除成功', 'success');
                    }
                });
            }
        }
    </script>
</body>
</html>
