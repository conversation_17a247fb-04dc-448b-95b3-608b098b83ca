# 工程背景
本项目为战略性矿产资源数据运营服务平台标段1数据底座（以下简称本项目）

# 建设目标
（1）建设运营平台数据底座，支撑数据全生命周期管理，采用先进的技术架构，保证高扩展性、高可用性、低成本、数据处理高效性。整合全产业链数据，搭建统一的数据采集与存储系统，制定数据标准，形成数据资产目录，以API接口等形式，支撑内外部应用调用数据。
（2）将数据与业务场景融合，借助低代码/无代码设计，降低数据分析技术门槛；通过人工智能、深度学习等交互方式，实现智能数据分析，驱动业务决策向智能化升级。

# 工程描述
生成一套静态原型页面，所有的页面均可通过点击跳转到其他页面，且所有页面均符合PC端设计规范，且界面美观大方，符合现代化UI设计风格。页面要求提供真实模拟数据，且所有页面均可通过点击跳转到其他页面，且所有页面均符合PC端设计规范，且界面美观大方，符合现代化UI设计风格。页面要求提供真实模拟数据。

## 功能描述
1. 数据底座功能模块
   - 统一存储
   需构建多源数据的统一入湖能力，支持各类结构化、非结构化及流数据的集成接入。
   具备PB级数据存储能力，支持存储节点弹性扩展，且扩展过程系统持续可用，无需人工介入。
   数据存储采用分布式节点部署模式，支持多副本存储机制。
   具备各类结构化、半结构化、非结构化PB级数据的低成本存储能力。
   ▲具备数据湖存储的弹性扩展能力，集群规模支持万级节点。
   需构建组件的高可靠运行架构，采用分布式冗余设计。
   具备高效的列式存储与压缩能力，支持根据数据特性动态适配压缩策略。
   具备多层次存储冗余与优化能力，支持结合多副本与先进编码技术实现存储高效利用。
   需构建完善的数据备份与恢复体系，支持灵活配置多时段备份策略。

   - 统一元数据管理
   至少支持离线计算、实时计算、数据湖表等元数据统一管理，支持统一元数据服务容器化部署，支持一份数据不同计算引擎共享。
   支持多租户隔离，保障不同租户元数据安全。
   支持不同计算引擎的元数据统一管理，支持数据资产在多场景计算分析环节的共享复用。
   需构建元数据服务的高可靠运行体系，支持冗余架构与容错机制。
   需构建适配国产化环境的统一元数据服务体系，支持主流国产数据库。
   具备Hive、Spark、Flink等多类计算引擎的元数据统一管控能力。
   
   - 统一权限管理
   需构建全域统一权限管控体系，支持不同计算与存储资源的权限协同管理。
   具备完善的多租户隔离体系，支持计算层客户端按租户维度获取权限策略，支持租户内多类身份的权限策略管理。
   需提供精细化数据权限管控能力，支持多维度资源的灵活权限配置与动态管控，保障数据访问的精准性与安全性。
   支持存储、计算及非结构化数据存储等多类资源权限管理，支持跨集群权限的集中化管控。
   需构建权限服务的高可靠运行架构，支持权限管理的不间断运行。
   支持权限审计。
   支持Hive、Spark、Flink等不少于7种计算存储资源权限统一管理。
   
   - 数据计算
   ★支持离线、实时等计算引擎容器化部署调度，具备任务级别资源横、纵向弹性扩缩能力，支持存算分离环境下跨集群的数据加工计算，至少具备Hive、Spark、Flink等元数据统一管理能力，支持跨引擎融合计算。
   需构建高效离线计算体系，具备PB级别数据批量处理能力。
   需采用分布式计算框架提供流式计算服务。
   需构建全域数据查询分析体系。
   需集成高性能算法引擎，具备SparkML支持能力。

   - 数据湖构建
   需构建批流一体数据湖存储体系，支持实时增量与离线批量数据的统一管控，具备一站式PB级数据的存储、计算与分析一体化能力。
   需具备实现精细化数据操作的能力，支持记录级别的更新与删除处理，具备数据增量处理机制。
   ▲支持对数据湖表小文件进行自动化合并处理，支持自运维。
   ▲具备数据湖格式的元数据统一纳管能力，实现实时、离线计算引擎基于数据湖内数据直接进行查询加工与分析。
   

   - 集群管控
   需具备集交付、监控、管理、管控、运营和服务为一体的集群管控平台，构建从集群建设、运维、支撑、运营到资源交付一体化的能力中心，实现集群的线上自动化部署和资源线上管理，提供面向运维的线上服务。
   提供支持集群上线自动化部署能力。
   ★支持对异构大数据集群的统一纳管，可一键运维集群，一览集群资源、服务组件的健康状态。具备大数据集群自动化巡检、小文件治理、任务分析等线上运维工具。
   支持多租户管理及租户间数据与权限隔离，支持租户内用户的基础管理。
   ▲支持租户内计算、存储、数据权限自运营。
   支持租户按照产品类型发起资源订购，用户按需发起资源申请、扩缩容和释放。
   支持租户发起资源的申请、扩缩容和释放后生成工单，工单可按需配置审批规则和资源规划规则。
   支持租户发起的资源工单审批通过自动化开通存储资源、计算资源，无需人工干预。
   支持资源自动化开通后，按照灵活配置的检测脚本自动化测试资源的健康状态。

2. 数据开发治理功能模块
   - 元数据模块
   ★需构建多源数据统一接入与治理支撑体系，兼容当前主流数据库环境（包括但不限于达梦、TiDB、mysql、hive、oracle、postgresql、sqlserver等），支持各类业务场景下的异构数据接入。
   需具备完善的数据源管理能力，支持主流关系型数据库及大数据平台数据源的接入管理；提供数据源连接信息配置与自动连通性校验功能；具备元数据自动识别与目录化存储能力。
   需构建完善的元数据目录体系，支持按多层级结构组织与展示元数据，可快速切换不同数据源进行查询，支持全面呈现字段的核心属性信息。
   需具备元数据自动同步能力，支持全量与增量同步策略，可配置周期触发机制；记录每次同步的时间、状态及异常信息，支持手动触发重同步。

   - 数据集成模块
   需具备完善的离线数据集成能力，支持主流数据源间的离线数据同步，提供字段级映射、任务调度及全流程执行监控功能。
   具备加载数据文件的数据集成能力，支持SFTP目录文件的校验和数据加载。
   ▲需具备实时数据集成能力，支持主流数据库（包括但不限于达梦、TiDB、mysql、hive、oracle、postgresql、sqlserver等）的数据变更实时捕获、Kafka等消息中间件的实时消息捕获。
   需提供灵活的任务配置能力，支持主流数据源间的双向数据流转配置；提供字段级映射功能，可满足命名调整、类型适配等需求；支持多种数据写入策略，适配全量更新、增量同步等场景。
   提供灵活的任务调度机制，支持即时执行与周期性调度，可配置任务执行优先级及超时控制策略；具备分布式任务分配能力。
   需具备完善的任务监控能力，支持实时展示任务执行进度、耗时及数据处理量，提供日志实时查看与留存下载功能；具备任务异常处理机制，支持自动重试与手动干预（含重试、跳过操作）。
   
   - 数据开发模块
   ★以流程开发为核心，支持从流程定义到实例运营的全生命周期管理；支持流程化开发与任务协同编排，具备可视化流程设计、周期调度及任务验证能力。
   需具备流程定义的功能，支持通过可视化方式编排各类任务节点，兼容数据结构查询、接口调用、数据转换等任务类型；支持变量的全局配置与节点间传递。
   提供完善的任务调度与运行管控能力，支持配置任务执行周期、起止范围及异常处理规则，并提供运行状态通知机制；具备任务实例全链路监控能力，支持按节点维度进行状态重置与接续运行。
   需具备完善的任务调试功能，支持对任务节点执行过程进行详细记录查询，可通过条件筛选快速定位异常信息；具备变量实时查看与调整能力，支持在调试过程中修改参数并重新执行。
   
   - 数据同步模块
   需构建适配企业多场景的数据采集体系，支持直观化操作界面、异构数据兼容处理能力，确保数据同步的精准性与持续稳定性。具备数据对接能力，支持多种方式（包括但不限于流、批、离线、实时等）。
   提供对主流数据库的离线同步的支持，可适配多种异构数据环境。
   ▲提供可视化表结构自动同步的能力，支持全面解析源数据库表结构信息；支持识别并适配分区表的分区策略，实现跨数据库类型的语法转换；具备数据库类型自动适配能力，可生成目标库兼容的结构定义语句并执行。
   需具备字段自动映射与数据类型转换能力，支持根据字段特征或预设规则实现源端与目标端字段对齐；可完成基础数据类型的自动适配，同时提供自定义转换能力；转换过程中生成详细日志记录，支持导出核查。
   提供灵活的任务调度与全面的执行监控能力，支持周期性执行与多场景触发机制；具备任务执行状态实时可视化能力，可展示进度、数据处理量及耗时等核心指标；建立完善的异常告警机制，在任务执行异常时主动推送告警信息并附带问题说明与处理建议。

   - 质量中心模块
   ★需构建全维度数据质量管理体系，覆盖质量全局视图、质量管控、质量评估及系统配置。
   需具备灵活的规则配置能力，支持单表维度的多类质量检查。
   ▲需提供多表关联规则配置能力，支持表间数据特征比对。
   需具备稽核任务自动执行与监控能力，支持通过本地模式执行规则校验逻辑，生成异常数据视图，支持稽核过程的自动化与结果可追溯。
   需具备完善的告警机制，支持配置阈值判定规则；支持按规则关联多个接收对象，告警信息包含问题详情及处理建议。
   提供数据质量报告自动化的体系，支持按日、周、月等周期生成包含质量评分、规则执行效能、问题分布特征的报告；支持可视化趋势呈现功能，以直观图表形式展示数据质量变化规律。

   - 数据资产模块
   ★需构建成熟的数据资产管理体系，具备资产目录化呈现与运营分析能力。
   需具备数据资产全流程配置与发布能力。
   ▲需具备构建统一数据资产目录能力。
   需具备数据资产运营分析能力。

   - 数据服务模块
   ★需构建企业级数据服务模块，支持服务封装，服务管理，服务运营信息展示。
   ▲需提供多样化数据服务封装能力，包括但不限于文件服务封装、API服务封装等。
   需具备全类型服务生命周期管理能力，覆盖服务发布，服务目录，已订阅服务。
   需提供数据服务订阅全流程管理能力，支持文件服务订阅，API服务订阅等。
   需提供数据服务运营信息全景展示能力，支持服务发布明细，服务订阅明细。

   - 数据录入工具模块
   ★提供数据录入工具，具备零代码能力，支持通过拖拉拽的方式创建表单和工作流程。提供不少于80个表单的设计实施服务。支持ipv6和ipv4双栈访问。采用标准的权限模型。移动端支持与中国矿产资源集团移动办公平台APP集成。本模块支持与OA系统和统一认证管理系统集成。提供从本模块到达梦数据库的完整的表单和数据同步方案。
   ▲需建立数据管理体系。
   ▲支持多种视图类型与过滤能力。
   支持用户角色与权限配置。
   需具备数据分析能力。
   ▲需具备工作流管理与可视化操作体系。
   支持完整的应用和后台管理能力。
   提供全面、完整的产品部署、升级、备份、故障处理方案

3. 数据分析功能模块
   - 智能对话交互界面
   ★具备交互式数据查询与分析能力，支持代码与图表的可视化呈现。

   - 数据接入管理
   具备多源数据接入与管理能力。

   - 数据智能体管理
   ▲具备数据智能体的全生命周期管理能力。

   - 标准词库配置
   需具备多维度数据名称的智能适配机制。

   - 业务术语配置
   ▲具备业务术语的配置和管理能力。

   - 任务规划样例配置
   具备任务规划样例的配置和管理能力。

   - 业务分析思路配置
   ▲具备业务分析思路的配置和管理能力。

   - 数据权限管理
   具备系统用户的全生命周期管理能力。

   - SQL案例管理
   ▲具备SQL问答对和SQL骨架的配置和管理能力。

   - 运营管理
   ▲具备基于用户历史对话问答的管理和数据回流能力。

   - 洞察结果生成和下载
   具备基于洞察或分析结果的下载能力，一键生成Word报告。

4. 数据可视化功能模块
   - 数据分析模块
   ★自助分析可提供业务人员便捷取数及深度交互式数据探索分析需求，支持数据的直观呈现与即时应用。
   支持类Excel的便携式操作方式。
   支持可视化分析流程，清晰呈现分析逻辑。
   支持自动记录用户的数据分析全过程。

   - 数据可视化与报告模块
   自助图表可直连多类数据库支持零代码拖拽交互。
   1)、支持各类平台对接，进行报表挂载。
   2)、支持各类报表与图表的配置。
   3)、支持多环境数据源接入。
   4)、提供多样化可视化组件。
   5)、支持灵活的下钻、上卷、联动、跳转等操作。
   6).提供丰富的大屏/领导驾驶舱模板，要求内嵌不少于20种大屏/驾驶舱样例。
   ▲PBI可直连多类数据库，支持零代码拖拽交互。 
   1)、支持线上演示文档制作。 
   2)、支持导出至本地进行二次编辑。 
   3)、提供多样化可视化组件。
   4)、支持中国式复杂的实现，包含多级嵌套表头、表尾、斜线表头、合并单元格，并支持自定义打印格式。
   5)、支撑与数据录入工具模块集成，提升数据录入工具模块报表能力。


5. 数据运营服务门户
   - 租户管理
   ▲多租户隔离：每个租户空间独立管理数据，支持成员添加及权限分配（管理员/普通用户）。
   租户切换：用户可在首页一键切换租户，实时加载对应数据视图。

   - 用户与角色管理
   ▲新增用户：支持用户名、密码、所属部门、角色分配。
   角色管理：定义角色功能权限。
   部门管理：支持多层级部门结构，部门与应用权限关联。

   - 公告发布
   公告发布：设置标题、类型、生效时间，支持富文本编辑，公告在首页弹窗展示。

   - 单点登录集成
   企业认证对接：兼容主流单点登录协议，同步企业身份系统用户信息。
   属性自动匹配：根据用户部门、职责动态分配归属租户、角色。

   - 用户管理升级
   跨租户权限映射：同一用户可拥有多个租户的不同角色权限。
   服务账号支持：为自动化任务创建专属账号，限制最小操作权限

   - 角色权限深度控制
   功能权限精细化：控制菜单访问、按钮操作。
   数据级授权：按数据库、表、字段分配数据可见性与操作权。

   - 数据授权管理
   灵活策略引擎：支持按用户属性授权，支持按角色模板批量授权。
   安全审批机制：高风险权限需人工审批，权限自动过期与续期提醒。

## 技术要求
1、只生成静态原型页面，不使用后端框架和数据库，所有数据均为模拟数据。
1、使用前端 html + Tailwind CSS (或Bootstrap) + js 生成所有原型界面，并使用 FontAwesome (或其他开源UI组件)让界面更加精美、接近真实的PC设计。
2、使用 iframe 方式嵌入各个界面，保持结构清晰。左侧菜单栏可折叠，点击菜单项时，右侧内容区域加载对应的页面。
3、每个界面应作为独立的 HTML 文件存放，例如 login.html、profile.html、settings.html 等。
4、页面设计应符合现代化UI设计风格，界面美观大方，且提供真实模拟数据。
5、确保所有页面均可通过点击跳转到其他页面，且所有页面均符合PC端设计规范。
6、页面应具备良好的用户体验，交互逻辑清晰，操作便捷。
7、不使用后端框架，所有页面均为静态页面。数据使用模拟数据。
