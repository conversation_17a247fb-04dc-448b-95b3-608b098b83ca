// 主要JavaScript逻辑
document.addEventListener('DOMContentLoaded', function() {
    initializeApp();
});

function initializeApp() {
    // 初始化菜单切换
    initMenuToggle();
    
    // 初始化用户下拉菜单
    initUserDropdown();
    
    // 初始化菜单组展开/折叠
    initMenuGroups();
    
    // 默认加载仪表板页面
    loadPage('pages/dashboard.html');
}

// 菜单切换功能
function initMenuToggle() {
    const menuToggle = document.getElementById('menuToggle');
    const sidebar = document.getElementById('sidebar');
    const mainContent = document.getElementById('mainContent');
    
    menuToggle.addEventListener('click', function() {
        sidebar.classList.toggle('collapsed');
        if (sidebar.classList.contains('collapsed')) {
            mainContent.style.marginLeft = '4rem';
        } else {
            mainContent.style.marginLeft = '16rem';
        }
    });
}

// 用户下拉菜单
function initUserDropdown() {
    const userMenu = document.getElementById('userMenu');
    const userDropdown = document.getElementById('userDropdown');
    
    userMenu.addEventListener('click', function(e) {
        e.stopPropagation();
        userDropdown.classList.toggle('hidden');
    });
    
    // 点击其他地方关闭下拉菜单
    document.addEventListener('click', function() {
        userDropdown.classList.add('hidden');
    });
}

// 菜单组展开/折叠
function initMenuGroups() {
    const menuGroupToggles = document.querySelectorAll('.menu-group-toggle');
    
    menuGroupToggles.forEach(toggle => {
        toggle.addEventListener('click', function() {
            const content = this.nextElementSibling;
            const icon = this.querySelector('.fa-chevron-down');
            
            // 切换展开/折叠状态
            content.classList.toggle('hidden');
            content.classList.toggle('show');
            this.classList.toggle('active');
            
            // 旋转箭头图标
            if (content.classList.contains('show')) {
                icon.style.transform = 'rotate(180deg)';
            } else {
                icon.style.transform = 'rotate(0deg)';
            }
        });
    });
}

// 加载页面到iframe
function loadPage(pageUrl) {
    const contentFrame = document.getElementById('contentFrame');
    const loadingIndicator = showLoading();
    
    contentFrame.src = pageUrl;
    
    contentFrame.onload = function() {
        hideLoading(loadingIndicator);
        
        // 更新活动菜单项
        updateActiveMenuItem(pageUrl);
    };
    
    contentFrame.onerror = function() {
        hideLoading(loadingIndicator);
        showToast('页面加载失败', 'error');
    };
}

// 更新活动菜单项
function updateActiveMenuItem(pageUrl) {
    // 移除所有活动状态
    document.querySelectorAll('.menu-item a, .menu-group-content a').forEach(item => {
        item.classList.remove('bg-blue-50', 'text-blue-600');
        item.classList.add('text-gray-700');
    });
    
    // 添加活动状态到当前菜单项
    const activeItem = document.querySelector(`[onclick="loadPage('${pageUrl}')"]`);
    if (activeItem) {
        activeItem.classList.remove('text-gray-700');
        activeItem.classList.add('bg-blue-50', 'text-blue-600');
        
        // 如果是子菜单项，展开父菜单组
        const parentGroup = activeItem.closest('.menu-group');
        if (parentGroup) {
            const toggle = parentGroup.querySelector('.menu-group-toggle');
            const content = parentGroup.querySelector('.menu-group-content');
            
            content.classList.remove('hidden');
            content.classList.add('show');
            toggle.classList.add('active');
            toggle.querySelector('.fa-chevron-down').style.transform = 'rotate(180deg)';
        }
    }
}

// 显示加载指示器
function showLoading() {
    const loading = document.createElement('div');
    loading.className = 'fixed top-20 right-4 bg-white p-3 rounded-lg shadow-lg z-50';
    loading.innerHTML = '<div class="loading"></div><span class="ml-2">加载中...</span>';
    document.body.appendChild(loading);
    return loading;
}

// 隐藏加载指示器
function hideLoading(loadingElement) {
    if (loadingElement && loadingElement.parentNode) {
        loadingElement.parentNode.removeChild(loadingElement);
    }
}

// 显示消息提示
function showToast(message, type = 'info', duration = 3000) {
    const toast = document.createElement('div');
    toast.className = `toast toast-${type}`;
    toast.textContent = message;
    
    document.body.appendChild(toast);
    
    // 自动移除提示
    setTimeout(() => {
        if (toast.parentNode) {
            toast.parentNode.removeChild(toast);
        }
    }, duration);
}

// 显示模态框
function showModal(title, content, buttons = []) {
    const modal = document.createElement('div');
    modal.className = 'modal';
    
    const modalContent = `
        <div class="modal-content">
            <div class="modal-header">
                <h3 class="modal-title">${title}</h3>
                <button class="modal-close" onclick="closeModal(this)">&times;</button>
            </div>
            <div class="modal-body">
                ${content}
            </div>
            <div class="modal-footer flex justify-end space-x-2 mt-4">
                ${buttons.map(btn => `<button class="btn ${btn.class || 'btn-primary'}" onclick="${btn.onclick || 'closeModal(this)'}">${btn.text}</button>`).join('')}
            </div>
        </div>
    `;
    
    modal.innerHTML = modalContent;
    document.body.appendChild(modal);
    
    // 点击背景关闭模态框
    modal.addEventListener('click', function(e) {
        if (e.target === modal) {
            closeModal(modal);
        }
    });
    
    return modal;
}

// 关闭模态框
function closeModal(element) {
    const modal = element.closest('.modal');
    if (modal && modal.parentNode) {
        modal.parentNode.removeChild(modal);
    }
}

// 确认对话框
function showConfirm(message, onConfirm, onCancel) {
    const buttons = [
        { text: '取消', class: 'btn-outline', onclick: 'closeModal(this)' },
        { text: '确认', class: 'btn-primary', onclick: `closeModal(this); (${onConfirm.toString()})()` }
    ];
    
    showModal('确认操作', `<p>${message}</p>`, buttons);
}

// 格式化日期
function formatDate(date, format = 'YYYY-MM-DD HH:mm:ss') {
    if (!(date instanceof Date)) {
        date = new Date(date);
    }
    
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    const hours = String(date.getHours()).padStart(2, '0');
    const minutes = String(date.getMinutes()).padStart(2, '0');
    const seconds = String(date.getSeconds()).padStart(2, '0');
    
    return format
        .replace('YYYY', year)
        .replace('MM', month)
        .replace('DD', day)
        .replace('HH', hours)
        .replace('mm', minutes)
        .replace('ss', seconds);
}

// 格式化文件大小
function formatFileSize(bytes) {
    if (bytes === 0) return '0 B';
    
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB', 'TB', 'PB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

// 格式化数字
function formatNumber(num) {
    if (num >= 1000000000) {
        return (num / 1000000000).toFixed(1) + 'B';
    }
    if (num >= 1000000) {
        return (num / 1000000).toFixed(1) + 'M';
    }
    if (num >= 1000) {
        return (num / 1000).toFixed(1) + 'K';
    }
    return num.toString();
}

// 生成随机ID
function generateId() {
    return Date.now().toString(36) + Math.random().toString(36).substr(2);
}

// 防抖函数
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

// 节流函数
function throttle(func, limit) {
    let inThrottle;
    return function() {
        const args = arguments;
        const context = this;
        if (!inThrottle) {
            func.apply(context, args);
            inThrottle = true;
            setTimeout(() => inThrottle = false, limit);
        }
    }
}

// 表格排序功能
function sortTable(table, column, direction = 'asc') {
    const tbody = table.querySelector('tbody');
    const rows = Array.from(tbody.querySelectorAll('tr'));
    
    rows.sort((a, b) => {
        const aVal = a.cells[column].textContent.trim();
        const bVal = b.cells[column].textContent.trim();
        
        // 尝试数字比较
        const aNum = parseFloat(aVal);
        const bNum = parseFloat(bVal);
        
        if (!isNaN(aNum) && !isNaN(bNum)) {
            return direction === 'asc' ? aNum - bNum : bNum - aNum;
        }
        
        // 字符串比较
        return direction === 'asc' ? aVal.localeCompare(bVal) : bVal.localeCompare(aVal);
    });
    
    // 重新插入排序后的行
    rows.forEach(row => tbody.appendChild(row));
}

// 表格搜索功能
function searchTable(table, searchTerm) {
    const tbody = table.querySelector('tbody');
    const rows = tbody.querySelectorAll('tr');
    
    rows.forEach(row => {
        const text = row.textContent.toLowerCase();
        const matches = text.includes(searchTerm.toLowerCase());
        row.style.display = matches ? '' : 'none';
    });
}

// 全局错误处理
window.addEventListener('error', function(e) {
    console.error('Global error:', e.error);
    showToast('系统发生错误，请刷新页面重试', 'error');
});

// 全局未处理的Promise拒绝
window.addEventListener('unhandledrejection', function(e) {
    console.error('Unhandled promise rejection:', e.reason);
    showToast('操作失败，请重试', 'error');
});
