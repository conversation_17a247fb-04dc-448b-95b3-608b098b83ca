<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>战略性矿产资源数据运营服务平台</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <link rel="stylesheet" href="css/style.css">
</head>
<body class="bg-gray-100 font-sans">
    <!-- 顶部导航栏 -->
    <nav class="bg-blue-600 text-white shadow-lg fixed w-full top-0 z-50">
        <div class="flex items-center justify-between px-4 py-3">
            <div class="flex items-center space-x-4">
                <button id="menuToggle" class="text-white hover:bg-blue-700 p-2 rounded">
                    <i class="fas fa-bars"></i>
                </button>
                <h1 class="text-xl font-bold">战略性矿产资源数据运营服务平台</h1>
            </div>
            <div class="flex items-center space-x-4">
                <div class="relative">
                    <button class="flex items-center space-x-2 hover:bg-blue-700 p-2 rounded">
                        <i class="fas fa-bell"></i>
                        <span class="bg-red-500 text-xs rounded-full px-1">3</span>
                    </button>
                </div>
                <div class="relative">
                    <button id="userMenu" class="flex items-center space-x-2 hover:bg-blue-700 p-2 rounded">
                        <i class="fas fa-user-circle text-2xl"></i>
                        <span>管理员</span>
                        <i class="fas fa-chevron-down"></i>
                    </button>
                    <div id="userDropdown" class="absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg py-1 hidden">
                        <a href="#" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">个人设置</a>
                        <a href="#" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">修改密码</a>
                        <hr class="my-1">
                        <a href="login.html" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">退出登录</a>
                    </div>
                </div>
            </div>
        </div>
    </nav>

    <div class="flex pt-16">
        <!-- 左侧菜单 -->
        <aside id="sidebar" class="bg-white shadow-lg w-64 min-h-screen transition-all duration-300">
            <div class="p-4">
                <nav class="space-y-2">
                    <!-- 首页 -->
                    <div class="menu-item">
                        <a href="#" onclick="loadPage('pages/dashboard.html')" class="flex items-center space-x-3 text-gray-700 p-3 rounded-lg hover:bg-blue-50 hover:text-blue-600">
                            <i class="fas fa-home"></i>
                            <span>首页仪表板</span>
                        </a>
                    </div>

                    <!-- 数据底座 -->
                    <div class="menu-group">
                        <button class="menu-group-toggle flex items-center justify-between w-full text-gray-700 p-3 rounded-lg hover:bg-blue-50 hover:text-blue-600">
                            <div class="flex items-center space-x-3">
                                <i class="fas fa-database"></i>
                                <span>数据底座</span>
                            </div>
                            <i class="fas fa-chevron-down transform transition-transform"></i>
                        </button>
                        <div class="menu-group-content hidden ml-6 mt-2 space-y-1">
                            <a href="#" onclick="loadPage('pages/storage.html')" class="block text-gray-600 p-2 rounded hover:bg-blue-50 hover:text-blue-600">统一存储</a>
                            <a href="#" onclick="loadPage('pages/metadata.html')" class="block text-gray-600 p-2 rounded hover:bg-blue-50 hover:text-blue-600">元数据管理</a>
                            <a href="#" onclick="loadPage('pages/permissions.html')" class="block text-gray-600 p-2 rounded hover:bg-blue-50 hover:text-blue-600">权限管理</a>
                            <a href="#" onclick="loadPage('pages/computing.html')" class="block text-gray-600 p-2 rounded hover:bg-blue-50 hover:text-blue-600">数据计算</a>
                            <a href="#" onclick="loadPage('pages/datalake.html')" class="block text-gray-600 p-2 rounded hover:bg-blue-50 hover:text-blue-600">数据湖管理</a>
                            <a href="#" onclick="loadPage('pages/cluster.html')" class="block text-gray-600 p-2 rounded hover:bg-blue-50 hover:text-blue-600">集群管控</a>
                        </div>
                    </div>

                    <!-- 数据开发治理 -->
                    <div class="menu-group">
                        <button class="menu-group-toggle flex items-center justify-between w-full text-gray-700 p-3 rounded-lg hover:bg-blue-50 hover:text-blue-600">
                            <div class="flex items-center space-x-3">
                                <i class="fas fa-cogs"></i>
                                <span>数据开发治理</span>
                            </div>
                            <i class="fas fa-chevron-down transform transition-transform"></i>
                        </button>
                        <div class="menu-group-content hidden ml-6 mt-2 space-y-1">
                            <a href="#" onclick="loadPage('pages/integration.html')" class="block text-gray-600 p-2 rounded hover:bg-blue-50 hover:text-blue-600">数据集成</a>
                            <a href="#" onclick="loadPage('pages/development.html')" class="block text-gray-600 p-2 rounded hover:bg-blue-50 hover:text-blue-600">数据开发</a>
                            <a href="#" onclick="loadPage('pages/sync.html')" class="block text-gray-600 p-2 rounded hover:bg-blue-50 hover:text-blue-600">数据同步</a>
                            <a href="#" onclick="loadPage('pages/quality.html')" class="block text-gray-600 p-2 rounded hover:bg-blue-50 hover:text-blue-600">质量中心</a>
                            <a href="#" onclick="loadPage('pages/assets.html')" class="block text-gray-600 p-2 rounded hover:bg-blue-50 hover:text-blue-600">数据资产</a>
                            <a href="#" onclick="loadPage('pages/services.html')" class="block text-gray-600 p-2 rounded hover:bg-blue-50 hover:text-blue-600">数据服务</a>
                            <a href="#" onclick="loadPage('pages/dataentry.html')" class="block text-gray-600 p-2 rounded hover:bg-blue-50 hover:text-blue-600">数据录入工具</a>
                        </div>
                    </div>

                    <!-- 数据分析 -->
                    <div class="menu-group">
                        <button class="menu-group-toggle flex items-center justify-between w-full text-gray-700 p-3 rounded-lg hover:bg-blue-50 hover:text-blue-600">
                            <div class="flex items-center space-x-3">
                                <i class="fas fa-chart-line"></i>
                                <span>数据分析</span>
                            </div>
                            <i class="fas fa-chevron-down transform transition-transform"></i>
                        </button>
                        <div class="menu-group-content hidden ml-6 mt-2 space-y-1">
                            <a href="#" onclick="loadPage('pages/chat.html')" class="block text-gray-600 p-2 rounded hover:bg-blue-50 hover:text-blue-600">智能对话</a>
                            <a href="#" onclick="loadPage('pages/agents.html')" class="block text-gray-600 p-2 rounded hover:bg-blue-50 hover:text-blue-600">数据智能体管理</a>
                            <a href="#" onclick="loadPage('pages/dictionary.html')" class="block text-gray-600 p-2 rounded hover:bg-blue-50 hover:text-blue-600">标准词库配置</a>
                            <a href="#" onclick="loadPage('pages/terminology.html')" class="block text-gray-600 p-2 rounded hover:bg-blue-50 hover:text-blue-600">业务术语配置</a>
                        </div>
                    </div>

                    <!-- 数据可视化 -->
                    <div class="menu-group">
                        <button class="menu-group-toggle flex items-center justify-between w-full text-gray-700 p-3 rounded-lg hover:bg-blue-50 hover:text-blue-600">
                            <div class="flex items-center space-x-3">
                                <i class="fas fa-chart-bar"></i>
                                <span>数据可视化</span>
                            </div>
                            <i class="fas fa-chevron-down transform transition-transform"></i>
                        </button>
                        <div class="menu-group-content hidden ml-6 mt-2 space-y-1">
                            <a href="#" onclick="loadPage('pages/analysis.html')" class="block text-gray-600 p-2 rounded hover:bg-blue-50 hover:text-blue-600">自助分析</a>
                            <a href="#" onclick="loadPage('pages/reports.html')" class="block text-gray-600 p-2 rounded hover:bg-blue-50 hover:text-blue-600">可视化报告</a>
                            <a href="#" onclick="loadPage('pages/dashboard-screen.html')" class="block text-gray-600 p-2 rounded hover:bg-blue-50 hover:text-blue-600">大屏展示</a>
                        </div>
                    </div>

                    <!-- 运营服务门户 -->
                    <div class="menu-group">
                        <button class="menu-group-toggle flex items-center justify-between w-full text-gray-700 p-3 rounded-lg hover:bg-blue-50 hover:text-blue-600">
                            <div class="flex items-center space-x-3">
                                <i class="fas fa-users-cog"></i>
                                <span>运营服务门户</span>
                            </div>
                            <i class="fas fa-chevron-down transform transition-transform"></i>
                        </button>
                        <div class="menu-group-content hidden ml-6 mt-2 space-y-1">
                            <a href="#" onclick="loadPage('pages/tenants.html')" class="block text-gray-600 p-2 rounded hover:bg-blue-50 hover:text-blue-600">租户管理</a>
                            <a href="#" onclick="loadPage('pages/users.html')" class="block text-gray-600 p-2 rounded hover:bg-blue-50 hover:text-blue-600">用户管理</a>
                            <a href="#" onclick="loadPage('pages/roles.html')" class="block text-gray-600 p-2 rounded hover:bg-blue-50 hover:text-blue-600">角色管理</a>
                            <a href="#" onclick="loadPage('pages/announcements.html')" class="block text-gray-600 p-2 rounded hover:bg-blue-50 hover:text-blue-600">公告管理</a>
                        </div>
                    </div>
                </nav>
            </div>
        </aside>

        <!-- 主内容区域 -->
        <main id="mainContent" class="flex-1 transition-all duration-300">
            <iframe id="contentFrame" src="pages/dashboard.html" class="w-full h-screen border-none"></iframe>
        </main>
    </div>

    <script src="js/main.js"></script>
</body>
</html>
